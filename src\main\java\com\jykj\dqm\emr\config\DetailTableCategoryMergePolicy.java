package com.jykj.dqm.emr.config;

import cn.hutool.core.util.ObjectUtil;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.emr.utils.CommonUtils;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 自定义动态表格，支持类别列合并单元格
 * 专门用于table1的类别列合并
 *
 * <AUTHOR>
 */
public class DetailTableCategoryMergePolicy extends DynamicTableRenderPolicy {
    private static final List<String> colNames = Arrays.asList("remark", "zero", "one", "two");

    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        if (null == data) return;

        CTTblPr tblPr = table.getCTTbl().getTblPr();
        tblPr.getTblW().setType(STTblWidth.DXA);
        //8503DXA≈15CM 9735  DXA≈17.17CM  1厘米≈567 DXA
        //650像素 17.17cm   17.17/650=0.0264
        int tableWidth = 9735;
        tblPr.getTblW().setW(BigInteger.valueOf(tableWidth));

        List<Map<String, Object>> targetRowData = (List<Map<String, Object>>) data;
        int oneRowLength = 360;
        int sequenceColumn = 698; // 约1.74cm*567=986   1.23*567=698
        //数据质量分析
        boolean qualityAnalysis = false;
        boolean hasRemark = false;
        int colTotalCount = 0;
        int colIndex = 0;
        if (ObjectUtil.isNotEmpty(targetRowData)) {
            colTotalCount = targetRowData.get(0).size();
            if(Objects.equals(targetRowData.get(0).get("remark"),"备注")){
                hasRemark = true;
            }
            if(Objects.equals(targetRowData.get(0).get("zero"),"类别")){
                qualityAnalysis = true;
            }
            table.removeRow(0);

            // 用于记录类别列的值，以便后续合并
            List<String> categoryList = new ArrayList<>();

            // 循环插入行数据
            for (int i = 0; i < targetRowData.size(); i++) {
                // 第一行是标题行
                XWPFTableRow xwpfTableRow = table.insertNewTableRow(i);
                // 循环列 row-cell
                colIndex = 0;
                for (Map.Entry<String, Object> vo : targetRowData.get(i).entrySet()) {
                    XWPFTableCell cell = xwpfTableRow.createCell();
                    CTTcPr tcpr = cell.getCTTc().addNewTcPr();
                    CTTblWidth cellw = tcpr.addNewTcW();

                    // 设置列宽
                    if (hasRemark && qualityAnalysis) {
                        if (colIndex == 0) {
                            // 类别列，较窄
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(sequenceColumn));
                        } else if (colIndex == 1 || colIndex == 2) {
                            // 要求项目列 和 医院项目列
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1568)); // 约2.78cm
                        } else {
                            // 剩余平分剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                                    .subtract(BigInteger.valueOf(sequenceColumn + 1568 + 1568));
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(colTotalCount - 3)));
                        }
                    } else if (hasRemark && !qualityAnalysis) {
                        if (colIndex == 0 || colIndex == 1) {
                            // 要求项目列 和 医院项目列
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1568)); // 约2.78cm
                        } else {
                            // 剩余平分剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                                    .subtract(BigInteger.valueOf(1568 + 1568));
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(colTotalCount - 2)));
                        }
                    } else if (qualityAnalysis && colTotalCount == 5) {
                        // 5列表格的列宽设置：类别、要求项目、医院项目、数据库表与字段名、数据字典表与字段名
                        if (colIndex == 0) {
                            // 类别列，较窄
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(sequenceColumn)); // 约1.74cm  *567
                        } else if (colIndex == 1 || colIndex == 2) {
                            // 要求项目列 和 医院项目列
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1568)); // 约2.78cm
                        } else {
                            // 剩余两列平分剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth)
                                    .subtract(BigInteger.valueOf(sequenceColumn + 1568 + 1568));
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(2)));
                        }
                    } else if (colTotalCount == 4) {
                        if (colIndex == 0) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1570)); //2.77*567
                        } else if (colIndex == 1) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(1815)); //3.2*567
                        } else {
                            // 计算剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(1570+1815)); // 减去前两个单元格的宽度
                            // 如果是4个单元格，后面的两个单元格平分剩余宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(2))); // 平分剩余的宽度
                        }
                    } else if (colTotalCount == 3) {
                        if (colIndex < 2) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(2835)); //5*567
                        } else {
                            // 计算剩余宽度
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(2835 * 2)); // 减去前两个单元格的宽度
                            // 如果是3个单元格，第三个单元格占满剩余宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth);
                        }
                    } else if (colTotalCount >= 5) {
                        //
                        if (colIndex == 0) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(oneRowLength * 2));
                        } else if (colIndex == 1) {
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(BigInteger.valueOf(oneRowLength * 7));
                        } else {
                            BigInteger remainingWidth = BigInteger.valueOf(tableWidth).subtract(BigInteger.valueOf(oneRowLength * 9)); // 减去前两个单元格的宽度
                            cellw.setType(STTblWidth.DXA);
                            cellw.setW(remainingWidth.divide(BigInteger.valueOf(colTotalCount - 2)));
                        }
                    } else {
                        cellw.setType(STTblWidth.DXA);
                        cellw.setW(BigInteger.valueOf(tableWidth / colTotalCount));
                    }

                    // 设置单元格垂直居中对齐
                    if ((qualityAnalysis && colIndex < 3) || (!qualityAnalysis && colIndex < 2) || (!qualityAnalysis && colTotalCount >= 5)) {
                        // 前3列垂直居中
                        tcpr.addNewVAlign().setVal(STVerticalJc.CENTER);
                    }

                    XWPFParagraph p = cell.getParagraphs().get(0);
                    // 前3列水平居中对齐，其他列左对齐
                    if ((qualityAnalysis && colIndex < 3) || (!qualityAnalysis && colIndex < 2) || (!qualityAnalysis && colTotalCount >= 5)) {
                        p.setAlignment(ParagraphAlignment.CENTER);
                    } else {
                        p.setAlignment(ParagraphAlignment.LEFT);
                    }
                    // create a run
                    XWPFRun r = p.createRun();
                    r.setFontFamily("宋体");
                    r.setFontSize(11);

                    // 单元格赋值
                    // 从当前单元格的CTTblWidth直接获取列宽，根据内容精确计算字符长度限制，实现自动换行
                    BigInteger cellWidth = getCellWidthFromTable(cell);
                    String cellContent = vo.getValue().toString();
                    int charLimit = calculateCharLimitByWidth(cellWidth, cellContent);
                    String string;
                    if (colNames.contains(vo.getKey())) {
                        string = CommonUtils.splitStringByLengthAndSubEnd(cellContent, charLimit);
                    } else {
                        string = CommonUtils.splitStringByLength(cellContent, charLimit);
                    }
                    String[] split = new String[]{string};
                    if (string.contains(Constant.LINE_SEPARATOR)) {
                        split = string.split(Constant.LINE_SEPARATOR);
                    } else if (string.contains("\n")) {
                        split = string.split("\n");
                    }

                    for (int j = 0; j < split.length; j++) {
                        if (j > 0) {
                            r.addBreak();
                        }
                        r.setText(split[j]);
                        //r.addCarriageReturn();
                    }

                    if (i == 0) {
                        // 标题行设置为粗体
                        r.setBold(true);
                    } else {
                        // 记录类别列的值（第一列，colIndex == 0）
                        if (colIndex == 0) {
                            categoryList.add(vo.getValue().toString());
                        }
                    }
                    colIndex++;
                }
            }
            if(qualityAnalysis){
                // 合并类别列的单元格
                mergeCategoryColumn(table, categoryList);
            }
            if(hasRemark){
                // 合并类别列的单元格
                mergeLastColumn(table, targetRowData.size() - 1, colIndex - 1);
            }
        }
    }

    /**
     * 从表格单元格中直接获取列宽度（DXA单位）
     *
     * @param cell 表格单元格
     * @return 列宽度（DXA单位），如果获取失败则返回默认值
     */
    private BigInteger getCellWidthFromTable(XWPFTableCell cell) {
        try {
            // 从单元格的CTTcPr中获取列宽设置
            CTTcPr tcPr = cell.getCTTc().getTcPr();
            if (tcPr != null && tcPr.getTcW() != null) {
                CTTblWidth tcW = tcPr.getTcW();
                if (tcW.getW() != null) {
                    Object width = tcW.getW();
                    if (width instanceof BigInteger) {
                        return (BigInteger) width;
                    }
                }
            }
        } catch (Exception e) {
            // 如果获取失败，记录日志但不抛出异常
            System.err.println("获取单元格宽度失败: " + e.getMessage());
        }

        // 如果无法获取到宽度，返回一个合理的默认值（约3cm）
        return BigInteger.valueOf(1701); // 3cm * 567 DXA/cm ≈ 1701 DXA
    }

    /**
     * 根据列宽度（DXA单位）和单元格内容精确计算字符长度限制
     * 分析文本中中英文字符比例，做出更准确的换行决策
     *
     * @param cellWidth 列宽度（DXA单位）
     * @param content 单元格内容
     * @return 字符长度限制
     */
    private int calculateCharLimitByWidth(BigInteger cellWidth, String content) {
        // DXA转换为厘米：1厘米≈567 DXA
        double widthInCm = cellWidth.doubleValue() / 567.0;

        // 宋体11号字体字符宽度（厘米）：
        double chineseCharWidth = 0.38; // 中文字符宽度
        double englishCharWidth = 0.2; // 英文/数字字符宽度

        // 分析文本内容，计算中英文字符比例
        TextAnalysis analysis = analyzeText(content);

        // 根据文本特征选择计算策略
        int charLimit;
        if (analysis.isChineseOnly()) {
            // 纯中文文本：按中文字符宽度计算
            charLimit = (int) Math.floor(widthInCm / chineseCharWidth);
        } else if (analysis.isEnglishOnly()) {
            // 纯英文文本：按英文字符宽度计算
            charLimit = (int) Math.floor(widthInCm / englishCharWidth);
        } else {
            // 中英文混合：根据比例加权计算平均字符宽度
            double avgCharWidth = analysis.chineseRatio * chineseCharWidth +
                                 analysis.englishRatio * englishCharWidth;
            charLimit = (int) Math.floor(widthInCm / avgCharWidth);

            // 对于混合文本，稍微保守一些，乘以0.9的安全系数
            charLimit = (int) (charLimit * 0.9);
        }

        // 设置最小和最大限制，避免过小或过大
        charLimit = Math.min(charLimit, 41); // 最多50个字符
        if (content.endsWith("对照记录数") || content.endsWith("表与字段名")) {
            return charLimit - 3;
        }
        return charLimit - 1;
    }

    /**
     * 分析文本内容，统计中英文字符比例
     *
     * @param text 要分析的文本
     * @return 文本分析结果
     */
    private TextAnalysis analyzeText(String text) {
        if (text == null || text.isEmpty()) {
            return new TextAnalysis(0, 0, 0.0, 0.0);
        }

        int chineseCount = 0;
        int englishCount = 0;

        for (char c : text.toCharArray()) {
            if (isChinese(c)) {
                chineseCount++;
            } else if (isEnglishOrDigit(c)) {
                englishCount++;
            }
            // 忽略其他字符（标点符号等）
        }

        int totalCount = chineseCount + englishCount;
        double chineseRatio = totalCount > 0 ? (double) chineseCount / totalCount : 0.0;
        double englishRatio = totalCount > 0 ? (double) englishCount / totalCount : 0.0;

        return new TextAnalysis(chineseCount, englishCount, chineseRatio, englishRatio);
    }

    /**
     * 判断字符是否为中文字符
     */
    private boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FFF; // 中文Unicode范围
    }

    /**
     * 判断字符是否为英文字母或数字
     */
    private boolean isEnglishOrDigit(char c) {
        return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9');
    }

    /**
     * 文本分析结果内部类
     */
    private static class TextAnalysis {
        final int chineseCount;
        final int englishCount;
        final double chineseRatio;
        final double englishRatio;

        TextAnalysis(int chineseCount, int englishCount, double chineseRatio, double englishRatio) {
            this.chineseCount = chineseCount;
            this.englishCount = englishCount;
            this.chineseRatio = chineseRatio;
            this.englishRatio = englishRatio;
        }

        boolean isChineseOnly() {
            return chineseCount > 0 && englishCount == 0;
        }

        boolean isEnglishOnly() {
            return englishCount > 0 && chineseCount == 0;
        }
    }

    /**
     * 合并类别列的单元格（第一列）
     *
     * @param table        XWPFTable
     * @param categoryList 类别列表
     */
    private void mergeCategoryColumn(XWPFTable table, List<String> categoryList) {
        if (categoryList.size() == 0) {
            return;
        }

        // 统计每个类别的出现次数，保持顺序
        Map<String, Integer> categoryCountMap = new LinkedHashMap<>();
        for (String category : categoryList) {
            categoryCountMap.put(category, categoryCountMap.getOrDefault(category, 0) + 1);
        }

        int startRowIndex = 1; // 从第二行开始（第一行是标题行）

        for (Map.Entry<String, Integer> entry : categoryCountMap.entrySet()) {
            int count = entry.getValue();
            if (count > 1) {
                // 需要合并的行数大于1时才进行合并
                int endRowIndex = startRowIndex + count - 1;
                mergeColumn(table, 0, startRowIndex, endRowIndex);
            }
            startRowIndex += count;
        }
    }

    /**
     * 合并最后一列单元格
     *
     * @param table        XWPFTable
     */
    private void mergeLastColumn(XWPFTable table, int endRowIndex, int col) {
        mergeColumn(table, col, 1, endRowIndex);
    }

    /**
     * 合并列
     *
     * @param table   表格
     * @param col     要合并的列
     * @param fromRow 开始的行
     * @param toRow   结束的行
     */
    public void mergeColumn(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            if (rowIndex == fromRow) {
                // 第一个合并的单元格设置为RESTART
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // 后续合并的单元格设置为CONTINUE
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }
}
