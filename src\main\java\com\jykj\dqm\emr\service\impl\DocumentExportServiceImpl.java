package com.jykj.dqm.emr.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.XWPFTemplate;
import com.jykj.dqm.auth.dao.SysUserMapper;
import com.jykj.dqm.auth.entity.SysUser;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import com.jykj.dqm.emr.dao.DocumentRuleConfigurationMapper;
import com.jykj.dqm.emr.entity.*;
import com.jykj.dqm.emr.manager.DbQueryNewUtil;
import com.jykj.dqm.emr.manager.PictureUtils;
import com.jykj.dqm.emr.manager.WordUtils;
import com.jykj.dqm.emr.manager.generateword.GenerateChapterWordFactory;
import com.jykj.dqm.emr.service.*;
import com.jykj.dqm.emr.task.DelayQueueManager;
import com.jykj.dqm.emr.task.DelayTask;
import com.jykj.dqm.emr.task.TaskBaseEntity;
import com.jykj.dqm.emr.utils.CommonUtils;
import com.jykj.dqm.emr.utils.PathNameUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.dao.MetadataDatasourceMapper;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.quality.manager.rulesql.BaseCheckSqlGenerator;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.SymmetricCryptoFactory;
import com.jykj.dqm.utils.HashCryptoFactory;
import com.jykj.dqm.utils.MapperUtils;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StpUtilMy;
import com.jykj.dqm.utils.StringUtil;
import com.jykj.dqm.utils.SystemUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 文档导出
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/23 14:47:11
 */
@Slf4j
@Service
public class DocumentExportServiceImpl implements DocumentExportService {
    @Autowired
    private DocumentDirectoryConfigurationService documentDirectoryConfigurationService;
    @Autowired
    private DocumentRuleConfigurationMapper documentRuleConfigurationMapper;
    @Autowired
    private DocumentExportRecordRuleDetailService documentExportRecordRuleDetailService;
    @Autowired
    private DocumentExportRecordService documentExportRecordService;
    @Autowired
    private HttpServletResponse response;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private GenerateChapterWordFactory generateChapterWordFactory;
    @Autowired
    private DelayQueueManager delayQueueManager;
    @Autowired
    private DataDictionaryDirectoryConfigurationService dataDictionaryDirectoryConfigurationService;
    @Autowired
    private DataDictionaryDirectoryRuleConfigurationService dataDictionaryDirectoryRuleConfigurationService;
    @Autowired
    private DbQueryNewUtil dbQueryNewUtil;
    @Autowired
    private MetadataDatasourceMapper metadataDatasourceMapper;
    @Autowired
    private DataScoreNumRecordService dataScoreNumRecordService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private DocumentRuleSqlExecRecordService documentRuleSqlExecRecordService;
    @Autowired
    private DataDictionaryAssociatedService dataDictionaryAssociatedService;
    @Autowired
    private RulePermissionConfigurationService rulePermissionConfigurationService;
    @Autowired
    private DocumentRuleConfigurationServiceImpl documentRuleConfigurationService;

    @Resource(name = "threadPoolMonitor")
    ThreadPoolExecutor threadPoolMonitor;

    /**
     * 预览导出
     *
     * @param documentExport DocumentExport
     * @return
     */
    @Override
    public R previewExport(DocumentExport documentExport) {
        String id = IdUtil.getSnowflakeNextIdStr();
        String path = SystemUtils.getFilePath() + "/word/" + id + "/";
        if (FileUtil.exist(path)) {
            FileUtil.del(path);
        }
        FileUtil.mkdir(path);
        DocumentExportRecord documentExportRecord = new DocumentExportRecord();
        documentExportRecord.setId(id);
        documentExportRecord.setProjectId(documentExport.getProjectId());
        documentExportRecord.setExportDocumentLevel(documentExport.getExportDocumentLevel());
        documentExportRecord.setDataStartTime(documentExport.getDataStartTime());
        documentExportRecord.setDataEndTime(documentExport.getDataEndTime());
        documentExportRecord.setExportDocumentTemplatesNum(0);
        String documentType = documentExport.getExportDocumentType();
        documentExportRecord.setExportDocumentType(documentType);
        documentExportRecord.setCreateBy(StpUtilMy.getUserAccount());
        // 定时导出
        String timedExportTime = documentExport.getTimedExportTime();
        if (StrUtil.isNotBlank(timedExportTime) && DateUtil.parse(timedExportTime).isAfter(new Date())) {
            documentExportRecord.setTimedExportTime(documentExport.getTimedExportTime());
            // 导出状态：1待生成、2生成中、3已完成
            documentExportRecord.setExportStatus("1");
            documentExportRecordService.save(documentExportRecord);
            long between = DateUtil.between(DateUtil.parse(timedExportTime), new Date(), DateUnit.MS, true);
            delayQueueManager.put(new DelayTask(new TaskBaseEntity(id, "previewExport"), between));
            return RUtil.success(documentExportRecord.getId());
        }
        Map<String, MetadataDatasource> map = getMetadataDatasourceMap();

        // 导出病历文档
        Integer num = export(id, path, documentExportRecord, documentExport);
        if (documentType.contains("1")) {
            // 导出基础数据（只写表，只查SQL）
            num += exportBaseData(documentExportRecord, map);
        }
        if (documentType.contains("2")) {
            // 导出病历数据（只写表，只查SQL）
            num += exportMedicalRecordData(documentExportRecord, map);
        }
        if (documentType.contains("3")) {
            // 导出质量数据（只写表，只查SQL）
            num += exportQualityData(documentExportRecord, map);
        }
        // 总数
        documentExportRecord.setExportDocumentTemplatesNum(num);
        // 导出状态：1待生成、2生成中、3已完成
        documentExportRecord.setExportStatus("2");
        documentExportRecordService.saveOrUpdate(documentExportRecord);
        redisTemplate.opsForValue().set(id + "_ALLNUM", num, 10, TimeUnit.HOURS);
        return RUtil.success(documentExportRecord.getId());
    }

    // 处理关联的数量
    @SneakyThrows
    @Override
    public void dealConditional(DocumentExportRecord documentExportRecord) {
        String exportRecordId = documentExportRecord.getId();
        List<DocumentRuleSqlExecRecord> numRecords = documentRuleSqlExecRecordService
                .list(Wrappers.<DocumentRuleSqlExecRecord>lambdaQuery().eq(DocumentRuleSqlExecRecord::getExportRecordId,
                        exportRecordId));
        Map<String, DocumentRuleSqlExecRecord> map = numRecords.stream()
                .collect(Collectors.toMap(
                        item -> item.getExportRecordId() + item.getDirectoryCode() + item.getDirectoryName()
                                + item.getEmrRuleType() + item.getRequiredProject(),
                        record -> record, (existing, replacement) -> existing));

        String documentType = documentExportRecord.getExportDocumentType();
        if (documentType.contains("1")) {
            // 处理基础
            dealBase(exportRecordId, documentExportRecord.getProjectId(), map);
        }
        if (documentType.contains("2") || documentType.contains("3")) {
            // 处理病历+质量
            dealMedicaltQuality(exportRecordId, documentExportRecord.getProjectId(), map);
        }
    }

    private void dealMedicaltQuality(String exportRecordId, String projectId,
            Map<String, DocumentRuleSqlExecRecord> map) {
        List<DataDictionaryDirectoryRuleConfiguration> medicaRecordsAndQualityList = dataDictionaryDirectoryRuleConfigurationService
                .list(new LambdaQueryWrapper<DataDictionaryDirectoryRuleConfiguration>()
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, projectId)
                        .in(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, new String[] { "2", "3" }));
        if (CollUtil.isEmpty(medicaRecordsAndQualityList)) {
            return;
        }
        List<String> directoryIds = medicaRecordsAndQualityList.stream()
                .map(DataDictionaryDirectoryRuleConfiguration::getId).collect(Collectors.toList());
        List<DataDictionaryAssociated> associatedList = dataDictionaryAssociatedService.list(Wrappers
                .<DataDictionaryAssociated>lambdaQuery().in(DataDictionaryAssociated::getDirectoryId, directoryIds));
        Map<String, List<DataDictionaryAssociated>> listMap = associatedList.stream()
                .collect(Collectors.groupingBy(item -> item.getDirectoryId() + "_" + item.getDataType()));

        List<DataScoreNumRecord> recordServices = dataScoreNumRecordService.list(
                Wrappers.<DataScoreNumRecord>lambdaQuery().eq(DataScoreNumRecord::getExportRecordId, exportRecordId));
        Map<String, DataScoreNumRecord> dataScoreNumRecordMap = recordServices.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryType() + item.getConfigurationId(), record -> record,
                        (existing, replacement) -> existing));
        for (DataDictionaryDirectoryRuleConfiguration directoryConfiguration : medicaRecordsAndQualityList) {
            // 目录类型（1、基础数据，2、病历数据，3、质量数据）
            DataScoreNumRecord dataScoreNumRecord = new DataScoreNumRecord().setExportRecordId(exportRecordId)
                    .setDirectoryType(directoryConfiguration.getDirectoryType())
                    .setConfigurationId(directoryConfiguration.getId());
            boolean flag = false;
            // 都有
            if ("1".equals(directoryConfiguration.getAssociatedType())) {
                // 映射（涉及公式）
                int num = getNum(listMap.get(directoryConfiguration.getId() + "_0"), map, exportRecordId);
                dataScoreNumRecord.setAllNum((long) num);
                dataScoreNumRecord.setAllNumType("1");
                flag = true;
            }

            // 质量数据才有
            if ("3".equals(directoryConfiguration.getDirectoryType())
                    && "1".equals(directoryConfiguration.getConditionalAssociatedType())) {
                // 映射（涉及公式）
                int num = getNum(listMap.get(directoryConfiguration.getId() + "_1"), map, exportRecordId);
                dataScoreNumRecord.setConditionalNum((long) num);
                dataScoreNumRecord.setConditionalNumType("1");
                flag = true;
            }
            if (!flag) {
                continue;
            }
            DataScoreNumRecord recordServiceOne = dataScoreNumRecordMap
                    .get(directoryConfiguration.getDirectoryType() + directoryConfiguration.getId());
            // 新增或者修改数据库里面现有的值
            if (recordServiceOne == null) {
                dataScoreNumRecordService.save(dataScoreNumRecord);
            } else {
                if ("1".equals(directoryConfiguration.getAssociatedType())) {
                    recordServiceOne.setAllNum(dataScoreNumRecord.getAllNum());
                    recordServiceOne.setAllNumType("1");
                }
                if ("3".equals(directoryConfiguration.getDirectoryType())
                        && "1".equals(directoryConfiguration.getConditionalAssociatedType())) {
                    recordServiceOne.setConditionalNum(dataScoreNumRecord.getConditionalNum());
                    recordServiceOne.setConditionalNumType("1");
                }
                dataScoreNumRecordService.updateById(recordServiceOne);
            }
        }
    }

    private void dealBase(String exportRecordId, String projectId, Map<String, DocumentRuleSqlExecRecord> map) {
        List<DataDictionaryDirectoryRuleConfiguration> ruleList = dataDictionaryDirectoryRuleConfigurationService
                .list(new LambdaQueryWrapper<>(DataDictionaryDirectoryRuleConfiguration.class)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, projectId)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, "1"));
        if (CollUtil.isEmpty(ruleList)) {
            return;
        }
        for (DataDictionaryDirectoryRuleConfiguration directoryConfiguration : ruleList) {
            if (!"1".equals(directoryConfiguration.getAssociatedType())) {
                continue;
            }
            List<DataDictionaryAssociated> dataDictionaryAssociateds = dataDictionaryAssociatedService
                    .list(new LambdaQueryWrapper<DataDictionaryAssociated>()
                            .eq(DataDictionaryAssociated::getDirectoryId, directoryConfiguration.getId()));
            directoryConfiguration.setAssociatedList(dataDictionaryAssociateds.stream()
                    .filter(item -> "0".equals(item.getDataType())).collect(Collectors.toList()));
            // directoryConfiguration.setConditionalAssociatedList(dataDictionaryAssociateds.stream().filter(item
            // -> "1".equals(item.getDataType())).collect(Collectors.toList()));
            // 映射（涉及公式）
            int num = getNum(directoryConfiguration.getAssociatedList(), map, exportRecordId);
            DataScoreNumRecord recordServiceOne = dataScoreNumRecordService.getOne(
                    Wrappers.<DataScoreNumRecord>lambdaQuery().eq(DataScoreNumRecord::getExportRecordId, exportRecordId)
                            .eq(DataScoreNumRecord::getDirectoryType, "1")
                            .eq(DataScoreNumRecord::getConfigurationId, directoryConfiguration.getId()));
            if (recordServiceOne == null) {
                // 关联类型（0：编辑SQL，1：选择数据及运算公式）
                DataScoreNumRecord dataScoreNumRecord = new DataScoreNumRecord().setExportRecordId(exportRecordId)
                        .setDirectoryType("1").setConfigurationId(directoryConfiguration.getId()).setAllNum((long) num)
                        .setAllNumType("1");
                dataScoreNumRecordService.save(dataScoreNumRecord);
            } else {
                recordServiceOne.setAllNum((long) num);
                dataScoreNumRecordService.updateById(recordServiceOne);
            }
        }
    }

    private int getNum(List<DataDictionaryAssociated> directoryConfiguration,
            Map<String, DocumentRuleSqlExecRecord> map, String exportRecordId) {
        int num = 0;
        if (directoryConfiguration == null) {
            return 0;
        }
        Integer recordNum;
        for (DataDictionaryAssociated dataDictionaryAssociated : directoryConfiguration) {
            DocumentRuleSqlExecRecord documentRuleSqlExecRecord = map
                    .get(exportRecordId + dataDictionaryAssociated.getAssociatedDirectoryCode()
                            + dataDictionaryAssociated.getAssociatedDirectoryName()
                            + dataDictionaryAssociated.getAssociatedEmrRuleType()
                            + dataDictionaryAssociated.getRequireProjectName());
            if (documentRuleSqlExecRecord == null) {
                continue;
            }
            if ("0".equals(dataDictionaryAssociated.getAssociatedDataCategory())) {
                recordNum = documentRuleSqlExecRecord.getRecordsNum();
            } else {
                recordNum = documentRuleSqlExecRecord.getConditionalRecordsNum();
            }
            if ("0".equals(dataDictionaryAssociated.getOperatorType())
                    || StrUtil.isBlank(dataDictionaryAssociated.getOperatorType())) {
                num += recordNum;
            }
            if ("1".equals(dataDictionaryAssociated.getOperatorType())) {
                num -= recordNum;
            }
        }
        return num;
    }

    /**
     * 执行定时导出任务
     *
     * @param exportRecordId 文档导出记录ID
     * @return
     */
    @Override
    public R previewExport(String exportRecordId) {
        log.warn("开始执行定时导出任务,任务ID：{}", exportRecordId);
        String id = exportRecordId;
        String path = SystemUtils.getFilePath() + "/word/" + id + "/";
        if (FileUtil.exist(path)) {
            FileUtil.del(path);
        }
        FileUtil.mkdir(path);
        DocumentExportRecord documentExportRecord = documentExportRecordService.getById(exportRecordId);
        DocumentExport documentExport = MapperUtils.INSTANCE.map(DocumentExport.class, documentExportRecord);
        String documentType = documentExport.getExportDocumentType();

        Map<String, MetadataDatasource> map = getMetadataDatasourceMap();
        // 导出状态：1待生成、2生成中、3已完成
        documentExportRecord.setExportStatus("2");
        // 先修改状态防止重复生成，定时任务操作的
        documentExportRecordService.saveOrUpdate(documentExportRecord);

        // 导出病历文档
        Integer num = export(id, path, documentExportRecord, documentExport);
        if (documentType.contains("1")) {
            // 导出基础数据（只写表，只查SQL）
            num += exportBaseData(documentExportRecord, map);
        }
        if (documentType.contains("2")) {
            // 导出病历数据（只写表，只查SQL）
            num += exportMedicalRecordData(documentExportRecord, map);
        }
        if (documentType.contains("3")) {
            // 导出质量数据（只写表，只查SQL）
            num += exportQualityData(documentExportRecord, map);
        }
        // 总数
        redisTemplate.opsForValue().set(id + "_ALLNUM", num, 10, TimeUnit.HOURS);

        documentExportRecord.setExportDocumentTemplatesNum(num);
        // 导出状态：1待生成、2生成中、3已完成
        documentExportRecord.setExportStatus("2");
        documentExportRecordService.saveOrUpdate(documentExportRecord);
        return RUtil.success(documentExportRecord.getId());
    }

    private Map<String, MetadataDatasource> getMetadataDatasourceMap() {
        List<MetadataDatasource> metadataDatasources = metadataDatasourceMapper
                .selectList(Wrappers.lambdaQuery(MetadataDatasource.class));
        Map<String, MetadataDatasource> map = new HashMap<>();
        metadataDatasources.stream().forEach(entity -> {
            entity.setDatabasePwd(SymmetricCryptoFactory.decrypt(entity.getDatabasePwd()));
            map.put(entity.getDataSourceId() + "", entity);
        });
        return map;
    }

    private Integer exportQualityData(DocumentExportRecord documentExportRecord,
            Map<String, MetadataDatasource> dataSourceMap) {
        String id = documentExportRecord.getId();
        List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService
                .getAllMedicaRecordsOrQuality(3);
        list = list.stream().filter(entity -> entity.getDirectoryCode().length() >= 7).collect(Collectors.toList());
        addRuleSql(documentExportRecord, 3, list);
        // 循环查询SQL
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : list) {
            CompletableFuture.runAsync(() -> {
                DataScoreNumRecord dataScoreNumRecord = new DataScoreNumRecord().setExportRecordId(id)
                        .setDirectoryType("3").setConfigurationId(directoryConfiguration.getId());
                try {
                    dealAll(documentExportRecord, directoryConfiguration, dataScoreNumRecord, dataSourceMap);
                    dealConditional(documentExportRecord, directoryConfiguration, dataScoreNumRecord, dataSourceMap);
                } finally {
                    recordRedisNumInfo(id, dataScoreNumRecord);
                }
            }, threadPoolMonitor);
        }
        return list.size();
    }

    private void addRuleSql(DocumentExportRecord documentExportRecord, int val,
            List<DataDictionaryDirectoryConfiguration> list) {
        // 赋值
        List<DataDictionaryDirectoryRuleConfiguration> ruleList = dataDictionaryDirectoryRuleConfigurationService
                .list(new LambdaQueryWrapper<>(DataDictionaryDirectoryRuleConfiguration.class)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, documentExportRecord.getProjectId())
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, val));
        Map<String, DataDictionaryDirectoryRuleConfiguration> directoryRuleConfigurationMap = ruleList.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode() + "_" + item.getDirectoryName(), item -> item,
                        (existing, replacement) -> existing));
        DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryRuleConfiguration;
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : list) {
            dataDictionaryDirectoryRuleConfiguration = directoryRuleConfigurationMap
                    .get(directoryConfiguration.getDirectoryCode() + "_" + directoryConfiguration.getDirectoryName());
            dataDictionaryDirectoryRuleConfiguration = dataDictionaryDirectoryRuleConfiguration == null
                    ? new DataDictionaryDirectoryRuleConfiguration()
                    : dataDictionaryDirectoryRuleConfiguration;
            // directoryConfiguration设置这些属性associatedType、conditionalAssociatedType、whetherCrossDbQuery、crossDbQueryDataSourceId、dataSourceId、conditionalDataSourceId、dataSql、conditionalDataSql、associatedList、conditionalAssociatedList、taskStatus
            directoryConfiguration.setId(dataDictionaryDirectoryRuleConfiguration.getId());
            directoryConfiguration.setAssociatedType(dataDictionaryDirectoryRuleConfiguration.getAssociatedType());
            directoryConfiguration.setConditionalAssociatedType(
                    dataDictionaryDirectoryRuleConfiguration.getConditionalAssociatedType());
            directoryConfiguration
                    .setWhetherCrossDbQuery(dataDictionaryDirectoryRuleConfiguration.getWhetherCrossDbQuery());
            directoryConfiguration.setCrossDbQueryDataSourceId(
                    dataDictionaryDirectoryRuleConfiguration.getCrossDbQueryDataSourceId());
            directoryConfiguration.setDataSourceId(dataDictionaryDirectoryRuleConfiguration.getDataSourceId());
            directoryConfiguration
                    .setConditionalDataSourceId(dataDictionaryDirectoryRuleConfiguration.getConditionalDataSourceId());
            directoryConfiguration.setDataSql(dataDictionaryDirectoryRuleConfiguration.getDataSql());
            directoryConfiguration
                    .setConditionalDataSql(dataDictionaryDirectoryRuleConfiguration.getConditionalDataSql());
            directoryConfiguration.setAssociatedList(dataDictionaryDirectoryRuleConfiguration.getAssociatedList());
            directoryConfiguration.setConditionalAssociatedList(
                    dataDictionaryDirectoryRuleConfiguration.getConditionalAssociatedList());
            directoryConfiguration.setTaskStatus(dataDictionaryDirectoryRuleConfiguration.getTaskStatus());
        }
    }

    private void dealConditional(DocumentExportRecord documentExportRecord,
            DataDictionaryDirectoryConfiguration directoryConfiguration, DataScoreNumRecord dataScoreNumRecord,
            Map<String, MetadataDatasource> dataSourceMap) {
        // 关联类型（0：编辑SQL，1：选择数据及运算公式）
        if (!"0".equals(directoryConfiguration.getConditionalAssociatedType())) {
            dataScoreNumRecord.setConditionalNumType("1");
            return;
        }
        // 查询SQL
        String sql = directoryConfiguration.getConditionalDataSql();
        Long all = 0L;
        if (StrUtil.isNotBlank(sql)) {
            MetadataDatasource metadataDatasource;
            if ("1".equals(directoryConfiguration.getWhetherCrossDbQuery())) {
                metadataDatasource = dataSourceMap.get(directoryConfiguration.getCrossDbQueryDataSourceId());
            } else {
                metadataDatasource = dataSourceMap.get(directoryConfiguration.getDataSourceId());
            }
            dataScoreNumRecord.setConditionalNumType("0");
            try {
                all = execSql(documentExportRecord.getDataStartTime(), documentExportRecord.getDataEndTime(), sql,
                        metadataDatasource).getValue();
            } catch (Exception e) {
                dataScoreNumRecord.setFailureReason(e.getMessage());
                dataScoreNumRecord.setExecStatus("1");
                throw new BusinessException(e.getMessage());
            }
            dataScoreNumRecord.setConditionalNum(all);

        }
    }

    private void dealAll(DocumentExportRecord documentExportRecord,
            DataDictionaryDirectoryConfiguration directoryConfiguration, DataScoreNumRecord dataScoreNumRecord,
            Map<String, MetadataDatasource> dataSourceMap) {
        // 关联类型（0：编辑SQL，1：选择数据及运算公式）
        if (!"0".equals(directoryConfiguration.getAssociatedType())) {
            dataScoreNumRecord.setAllNumType("1");
            return;
        }
        String sql = directoryConfiguration.getDataSql();
        Long all = 0L;
        if (StrUtil.isNotBlank(sql)) {
            // 查询SQL
            MetadataDatasource metadataDatasource;
            if ("1".equals(directoryConfiguration.getWhetherCrossDbQuery())) {
                metadataDatasource = dataSourceMap.get(directoryConfiguration.getCrossDbQueryDataSourceId());
            } else {
                metadataDatasource = dataSourceMap.get(directoryConfiguration.getDataSourceId());
            }
            dataScoreNumRecord.setAllNumType("0");
            try {
                all = execSql(documentExportRecord.getDataStartTime(), documentExportRecord.getDataEndTime(), sql,
                        metadataDatasource).getValue();
            } catch (Exception e) {
                dataScoreNumRecord.setFailureReason(e.getMessage());
                dataScoreNumRecord.setExecStatus("1");
                throw new BusinessException(e.getMessage());
            }
            dataScoreNumRecord.setAllNum(all);
        }
    }

    private Integer exportMedicalRecordData(DocumentExportRecord documentExportRecord,
            Map<String, MetadataDatasource> dataSourceMap) {
        List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService
                .getAllMedicaRecordsOrQuality(2);
        // 循环查询SQL
        list = list.stream().filter(entity -> entity.getDirectoryCode().length() >= 6).collect(Collectors.toList());
        // 赋值
        addRuleSql(documentExportRecord, 2, list);
        String id = documentExportRecord.getId();
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : list) {
            CompletableFuture.runAsync(() -> {
                DataScoreNumRecord dataScoreNumRecord = new DataScoreNumRecord().setExportRecordId(id)
                        .setDirectoryType("2").setConfigurationId(directoryConfiguration.getId());
                // 关联类型（0：编辑SQL，1：选择数据及运算公式）
                if (!"0".equals(directoryConfiguration.getAssociatedType())) {
                    dataScoreNumRecord.setAllNumType("1");
                    recordRedisNumInfo(id, dataScoreNumRecord);
                    return;
                }
                try {
                    // 查询SQL
                    MetadataDatasource metadataDatasource;
                    if ("1".equals(directoryConfiguration.getWhetherCrossDbQuery())) {
                        metadataDatasource = dataSourceMap.get(directoryConfiguration.getCrossDbQueryDataSourceId());
                    } else {
                        metadataDatasource = dataSourceMap.get(directoryConfiguration.getDataSourceId());
                    }
                    metadataDatasource
                            .setDatabasePwd(SymmetricCryptoFactory.decrypt(metadataDatasource.getDatabasePwd()));
                    String sql = directoryConfiguration.getDataSql();
                    dataScoreNumRecord.setAllNumType("0");
                    Long all = 0L;
                    if (StrUtil.isNotBlank(sql)) {
                        all = execSql(documentExportRecord.getDataStartTime(), documentExportRecord.getDataEndTime(),
                                sql, metadataDatasource).getValue();
                    }
                    dataScoreNumRecord.setAllNum(all);
                } catch (Exception e) {
                    e.printStackTrace();
                    dataScoreNumRecord.setFailureReason(e.getMessage());
                    dataScoreNumRecord.setExecStatus("1");
                } finally {
                    recordRedisNumInfo(id, dataScoreNumRecord);
                }
            }, threadPoolMonitor);
        }
        return list.size();
    }

    private Integer exportBaseData(DocumentExportRecord documentExportRecord,
            Map<String, MetadataDatasource> dataSourceMap) {
        List<DataDictionaryDirectoryConfiguration> list = (List<DataDictionaryDirectoryConfiguration>) dataDictionaryDirectoryConfigurationService
                .getBase().getData();
        // 赋值
        addRuleSql(documentExportRecord, 1, list);
        String id = documentExportRecord.getId();
        // 循环查询SQL
        List<CompletableFuture> futureList = new ArrayList<>();
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : list) {
            if ("0".equals(directoryConfiguration.getDirectoryCode())) {
                recordRedisNumInfo(id, null);
                continue;
            }
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 关联类型（0：编辑SQL，1：选择数据及运算公式）
                DataScoreNumRecord dataScoreNumRecord = new DataScoreNumRecord().setExportRecordId(id)
                        .setDirectoryType("1").setConfigurationId(directoryConfiguration.getId());

                if (!"0".equals(directoryConfiguration.getAssociatedType())) {
                    dataScoreNumRecord.setAllNumType("1");
                    recordRedisNumInfo(id, dataScoreNumRecord);
                    return;
                }
                try {
                    MetadataDatasource metadataDatasource;
                    if ("1".equals(directoryConfiguration.getWhetherCrossDbQuery())) {
                        metadataDatasource = dataSourceMap.get(directoryConfiguration.getCrossDbQueryDataSourceId());
                    } else {
                        metadataDatasource = dataSourceMap.get(directoryConfiguration.getDataSourceId());
                    }
                    // 查询SQL
                    String sql = directoryConfiguration.getDataSql();
                    dataScoreNumRecord.setAllNumType("0");
                    Long all = 0L;
                    if (StrUtil.isNotBlank(sql)) {
                        all = execSql(documentExportRecord.getDataStartTime(), documentExportRecord.getDataEndTime(),
                                sql, metadataDatasource).getValue();
                    }
                    dataScoreNumRecord.setAllNum(all);
                } catch (Exception e) {
                    e.printStackTrace();
                    dataScoreNumRecord.setFailureReason(e.getMessage());
                    dataScoreNumRecord.setExecStatus("1");
                } finally {
                    recordRedisNumInfo(id, dataScoreNumRecord);
                }
            }, threadPoolMonitor);
            futureList.add(future);
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
        return list.size();
    }

    private Pair<String, Long> execSql(String startDate, String endDate, String sql,
            MetadataDatasource metadataDatasource) {
        Pair<String, Long> result;
        if (StrUtil.isBlank(sql)) {
            result = new Pair<>(sql, 0L);
            return result;
        }
        if (sql.contains("#startDate") && sql.contains("#endDate")) {
            sql = addTimeToSql(sql, metadataDatasource.getDatabaseType(), startDate, endDate + " 23:59:59");
        }
        Long count = dbQueryNewUtil.queryCount(metadataDatasource, sql);
        result = new Pair<>(sql, count);
        return result;
    }

    String addTimeToSql(String sql, String dbType, String startDate, String endDate) {
        BaseCheckSqlGenerator baseCheckSqlGenerator = new BaseCheckSqlGenerator();
        String startTimeSql = baseCheckSqlGenerator.getSqlDateFunction(dbType, startDate);
        String endTimeSql = baseCheckSqlGenerator.getSqlDateFunction(dbType, endDate);
        return sql.replace("#startDate", startTimeSql).replace("#endDate", endTimeSql);
    }

    private Long recordRedisNumInfo(String recordId, DataScoreNumRecord dataScoreNumRecord) {
        Long increment = redisTemplate.opsForValue().increment(recordId);
        redisTemplate.expire(recordId, 10, TimeUnit.HOURS);
        if (dataScoreNumRecord != null && ("0".equals(dataScoreNumRecord.getAllNumType())
                || "0".equals(dataScoreNumRecord.getConditionalNumType()))) {
            dataScoreNumRecordService.save(dataScoreNumRecord);
        }
        return increment;
    }

    /**
     * 导出
     *
     * @param id
     * @param path
     * @param documentExportRecord 接收参数
     * @param documentExport       传递参数
     */
    private Integer export(String id, String path, DocumentExportRecord documentExportRecord,
            DocumentExport documentExport) {
        // 初始化图片
        PictureUtils pictureUtils = new PictureUtils();
        Map<String, String> params = new HashMap<>();
        String userName = getUserName(documentExportRecord.getCreateBy());
        params.put("exportUserName", userName);
        pictureUtils.combinePictureAddUserName(path + "picturetemplate.png", params);

        LambdaQueryWrapper<DocumentDirectoryConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.le(DocumentDirectoryConfiguration::getLevelCode, documentExport.getExportDocumentLevel());
        lambdaQueryWrapper.le(StrUtil.isNotBlank(documentExport.getExportDocumentLevel()),
                DocumentDirectoryConfiguration::getLevelCode, documentExport.getExportDocumentLevel());
        lambdaQueryWrapper.orderByAsc(DocumentDirectoryConfiguration::getDirectoryCode);
        lambdaQueryWrapper.select(DocumentDirectoryConfiguration::getSerialNum,
                DocumentDirectoryConfiguration::getDirectoryCode, DocumentDirectoryConfiguration::getDirectoryName,
                DocumentDirectoryConfiguration::getLevelCode, DocumentDirectoryConfiguration::getEmrRuleType);
        // lambdaQueryWrapper.apply("length(DIRECTORY_CODE) > {0}", "5");
        // 所有的一级、二级、三级标题目录
        List<DocumentDirectoryConfiguration> list = documentDirectoryConfigurationService.list(lambdaQueryWrapper);
        // 只要已分配的
        // 过滤目录
        // 所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = rulePermissionConfigurationService.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, "0")
                        .eq(RulePermissionConfiguration::getProjectId, documentExport.getProjectId()));
        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream()
                .collect(Collectors.toMap(
                        item -> item.getDirectoryCode() + item.getDirectoryName() + item.getEmrRuleType(), item -> item,
                        (k1, k2) -> k1));
        List<DocumentDirectoryConfiguration> listFilter = list;
        // 只要已分配的目录
        listFilter = listFilter.stream()
                .filter(item -> allAllocatedDirectoryMap
                        .containsKey(item.getDirectoryCode() + item.getDirectoryName() + item.getEmrRuleType()))
                .collect(Collectors.toList());
        Set<String> allowDirectoryCodes = new HashSet<>();
        String directoryCodeStr;
        for (DocumentDirectoryConfiguration directoryConfiguration : listFilter) {
            directoryCodeStr = directoryConfiguration.getDirectoryCode();
            if (directoryCodeStr.length() > 5) {
                allowDirectoryCodes.add(directoryCodeStr.substring(0, 2));
                allowDirectoryCodes.add(directoryCodeStr.substring(0, 5));
            }
            allowDirectoryCodes.add(directoryCodeStr);
        }
        listFilter = list.stream().filter(item -> allowDirectoryCodes.contains(item.getDirectoryCode()))
                .collect(Collectors.toList());
        // 生成文档目录模板（不包含文档正文前面的首页和目录页）
        generateCatalogDocumentTemplate(documentExport, path, listFilter);
        // 过滤三级标题目录
        List<DocumentDirectoryConfiguration> configurations = listFilter.stream()
                .filter(item -> item.getDirectoryCode().length() > 5).collect(Collectors.toList());
        // 设置变量
        TransmittableThreadLocalManager.set(userName);
        // 异步一个个文档处理 文档名：目录名称+目录编码+关联类型 directoryCode+directoryName+emrRuleType 去掉空格
        Map<String, DocumentDirectoryConfiguration> secondMap = listFilter.stream()
                .filter(directoryConfiguration -> directoryConfiguration.getDirectoryCode().length() == 5).distinct()
                .collect(Collectors.toMap(item -> item.getDirectoryCode(), item -> item, (k1, k2) -> k1));
        int count = dealPreviewExport(documentExport, id, path, configurations, secondMap);
        TransmittableThreadLocalManager.remove();
        // 处理：最新导出word时，可主动获取其他文档维护的最新批注信息，包括：说明、sql注释、原因、整改说明
        // 处理批注信息关联
        processAnnotationInfo(id, documentExport.getProjectId());

        return count;
    }

    /**
     * 处理批注信息关联
     * 从DQM_EMR_DOCUMENT_RULE_SQL_EXEC_RECORD获取记录，然后从DQM_EMR_DOCUMENT_EXPORT_RECORD_RULE_DETAIL
     * 批注信息：PROBLEM_DATA_REMARKS1、PROBLEM_DATA_REMARKS2、PROBLEM_DATA_REMARKS3、PROBLEM_DATA_REMARKS4
     * 通过DIRECTORY_CODE、DIRECTORY_NAME、EMR_RULE_TYPE关联查找之前的批注信息，有记录的写入DQM_EMR_DOCUMENT_EXPORT_RECORD_RULE_DETAIL
     * 注意DQM_EMR_DOCUMENT_EXPORT_RECORD_RULE_DETAIL是每次导出的批注信息，取最新的一次记录来关联
     *
     * @param exportRecordId 当前导出记录ID
     * @param projectId      项目ID
     */
    private void processAnnotationInfo(String exportRecordId, String projectId) {
        try {
            log.info("开始处理批注信息关联，导出记录ID：{}, 项目ID：{}", exportRecordId, projectId);

            // 1. 从DQM_EMR_DOCUMENT_RULE_SQL_EXEC_RECORD获取当前导出的记录
            List<DocumentRuleSqlExecRecord> currentExecRecords = documentRuleSqlExecRecordService.list(
                    new LambdaQueryWrapper<DocumentRuleSqlExecRecord>()
                            .eq(DocumentRuleSqlExecRecord::getExportRecordId, exportRecordId));

            if (CollUtil.isEmpty(currentExecRecords)) {
                log.info("当前导出记录中没有找到SQL执行记录，跳过批注信息处理");
                return;
            }

            // 2. 获取项目下最新的导出记录（排除当前导出记录）
            DocumentExportRecord latestExportRecord = documentExportRecordService.getOne(
                    new LambdaQueryWrapper<DocumentExportRecord>()
                            .eq(DocumentExportRecord::getProjectId, projectId)
                            .ne(DocumentExportRecord::getId, exportRecordId)
                            .orderByDesc(DocumentExportRecord::getCreateTime)
                            .last("LIMIT 1"));

            if (latestExportRecord == null) {
                log.info("项目{}下没有找到历史导出记录，跳过批注信息处理", projectId);
                return;
            }

            log.info("找到最新的历史导出记录：{}", latestExportRecord.getId());

            // 3. 获取最新导出记录的批注信息
            List<DocumentExportRecordRuleDetail> latestAnnotations = documentExportRecordRuleDetailService.list(
                    new LambdaQueryWrapper<DocumentExportRecordRuleDetail>()
                            .eq(DocumentExportRecordRuleDetail::getExportRecordId, latestExportRecord.getId())
                            .and(wrapper -> wrapper
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks1)
                                    .or()
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks2)
                                    .or()
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks3)
                                    .or()
                                    .isNotNull(DocumentExportRecordRuleDetail::getProblemDataRemarks4)));

            if (CollUtil.isEmpty(latestAnnotations)) {
                log.info("最新导出记录{}中没有找到批注信息，跳过批注信息处理", latestExportRecord.getId());
                return;
            }

            // 4. 按DIRECTORY_CODE、DIRECTORY_NAME、EMR_RULE_TYPE建立索引
            Map<String, DocumentExportRecordRuleDetail> latestAnnotationMap = new HashMap<>();
            for (DocumentExportRecordRuleDetail annotation : latestAnnotations) {
                String key = annotation.getDirectoryCode() + "_" + annotation.getDirectoryName() + "_"
                        + annotation.getEmrRuleType();
                latestAnnotationMap.put(key, annotation);
            }

            // 5. 为当前导出记录创建批注信息
            List<DocumentExportRecordRuleDetail> newAnnotations = new ArrayList<>();
            for (DocumentRuleSqlExecRecord execRecord : currentExecRecords) {
                String key = execRecord.getDirectoryCode() + "_" + execRecord.getDirectoryName() + "_"
                        + execRecord.getEmrRuleType();
                DocumentExportRecordRuleDetail latestAnnotation = latestAnnotationMap.get(key);

                if (latestAnnotation != null) {
                    // 找到了历史批注信息，创建新的批注记录
                    DocumentExportRecordRuleDetail newAnnotation = DocumentExportRecordRuleDetail.builder()
                            .exportRecordId(exportRecordId)
                            .directoryCode(execRecord.getDirectoryCode())
                            .directoryName(execRecord.getDirectoryName())
                            .emrRuleType(execRecord.getEmrRuleType())
                            .problemDataRemarks1(latestAnnotation.getProblemDataRemarks1())
                            .problemDataRemarks2(latestAnnotation.getProblemDataRemarks2())
                            .problemDataRemarks3(latestAnnotation.getProblemDataRemarks3())
                            .problemDataRemarks4(latestAnnotation.getProblemDataRemarks4())
                            .build();

                    newAnnotations.add(newAnnotation);
                    log.debug("为{}创建批注信息，来源：{}", key, latestAnnotation.getExportRecordId());
                }
            }

            // 6. 批量保存新的批注信息
            if (!newAnnotations.isEmpty()) {
                documentExportRecordRuleDetailService.saveBatch(newAnnotations);
                log.info("成功创建{}条批注信息关联记录", newAnnotations.size());
            } else {
                log.info("没有找到可关联的历史批注信息");
            }

        } catch (Exception e) {
            log.error("处理批注信息关联时发生异常：{}", e.getMessage(), e);
        }
    }

    private String getUserName(String loginId) {
        String userName = loginId;
        try {
            SysUser userInfo = sysUserMapper.getUserInfoByLoginId(loginId);
            if (userInfo != null) {
                userName = userInfo.getUserName();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return userName;
    }

    /**
     * 生成文档目录模板
     *
     * @param documentExport
     * @param path
     * @param list
     */
    private void generateCatalogDocumentTemplate(DocumentExport documentExport, String path,
            List<DocumentDirectoryConfiguration> list) {
        WordUtils wordUtils = new WordUtils();
        Map<String, String> params = new HashMap<>();
        params.put("year", DateUtil.date().year() + "");
        params.put("levelCode", LevelCodeEnum.fromCode(documentExport.getExportDocumentLevel()).getDescription());
        params.put("yearAndMouth", DateUtil.date().toString("yyyy年MM月"));
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("hospital.code");
        String hospitalCode = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
        sysConfigByName = RedisUtil.getSysConfigByName("hospital.name");
        String hospitalName = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
        params.put("hospitalCode", hospitalCode);
        params.put("hospitalName", hospitalName);
        List<DocumentDirectoryFirst> documentDirectorys = documentRuleConfigurationService
                .getDocumentDirectorysNew(list);
        wordUtils.generateCatalogDocumentTemplate(documentDirectorys, path, params);
    }

    @Override
    public R regeneratePreviewDocument(DocumentExportEachDoc documentExport) {
        DocumentExportRecord exportRecord = documentExportRecordService.getById(documentExport.getExportRecordId());
        String path = SystemUtils.getFilePath() + "/word/" + documentExport.getExportRecordId() + "/";
        if (!FileUtil.exist(path + "picturetemplate.png")) {
            // 初始化图片
            PictureUtils pictureUtils = new PictureUtils();
            Map<String, String> params = new HashMap<>();
            params.put("exportUserName", exportRecord.getCreateBy());
            pictureUtils.combinePictureAddUserName(path + "picturetemplate.png", params);
        }
        LambdaQueryWrapper<DocumentRuleConfiguration> lambdaQueryWrapper2 = Wrappers.lambdaQuery();
        lambdaQueryWrapper2.eq(DocumentRuleConfiguration::getDirectoryCode, documentExport.getDirectoryCode())
                .eq(DocumentRuleConfiguration::getDirectoryName, documentExport.getDirectoryName())
                .eq(DocumentRuleConfiguration::getEmrRuleType, documentExport.getEmrRuleType())
                .eq(DocumentRuleConfiguration::getProjectId, exportRecord.getProjectId());
        List<DocumentRuleConfiguration> documentRuleConfigurations = documentRuleConfigurationMapper
                .selectList(lambdaQueryWrapper2);
        // 异步处理每个章节
        DocumentDirectoryConfiguration directoryConfiguration = DocumentDirectoryConfiguration.builder()
                .directoryCode(documentExport.getDirectoryCode()).directoryName(documentExport.getDirectoryName())
                .emrRuleType(documentExport.getEmrRuleType()).levelCode(exportRecord.getExportDocumentLevel()).build();
        return generateChapterWordFactory.generateChapterWordSync(documentRuleConfigurations, directoryConfiguration,
                exportRecord.getDataStartTime(), exportRecord.getDataEndTime(), path,
                documentExport.getExportRecordId());
    }

    @Override
    public void downloadDoc(String exportRecordId) {
        DocumentExportRecord exportRecord = documentExportRecordService.getById(exportRecordId);
        String filePath = SystemUtils.getFilePath() + "/word/" + exportRecordId + "/";
        String fileName = "电子病历系统功能应用水平分级评价(" + exportRecord.getExportDocumentLevel() + "级).zip";
        FileInputStream fileInputStream = null;
        try {
            response.setContentType("application/zip;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("Content-Transfer-Encoding", "binary");
            List<File> files = new ArrayList<>();
            for (File file : FileUtil.loopFiles(filePath)) {
                if (file.getName().contains("电子病历系统功能应用水平分级评价")) {
                    files.add(file);
                }
            }
            File file = ZipUtil.zip(FileUtil.file(filePath + "all.zip"), false, files.toArray(new File[] {}));
            response.setHeader("Content-Transfer-Encoding", "binary");
            fileInputStream = new FileInputStream(file);
            IOUtils.copy(fileInputStream, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e);
        } finally {
            IoUtil.close(fileInputStream);
        }
    }

    private int dealPreviewExport(DocumentExport documentExport, String recordId, String path,
            List<DocumentDirectoryConfiguration> list, Map<String, DocumentDirectoryConfiguration> secondMap) {
        Map<String, List<DocumentRuleConfiguration>> listMap = new HashMap<>();
        List<DocumentRuleConfiguration> documentRuleConfigurations;
        for (DocumentDirectoryConfiguration directoryConfiguration : list) {
            LambdaQueryWrapper<DocumentRuleConfiguration> lambdaQueryWrapper2 = Wrappers.lambdaQuery();
            lambdaQueryWrapper2
                    .eq(DocumentRuleConfiguration::getDirectoryCode, directoryConfiguration.getDirectoryCode())
                    .eq(DocumentRuleConfiguration::getDirectoryName, directoryConfiguration.getDirectoryName())
                    .eq(DocumentRuleConfiguration::getEmrRuleType, directoryConfiguration.getEmrRuleType())
                    .eq(DocumentRuleConfiguration::getProjectId, documentExport.getProjectId());
            documentRuleConfigurations = documentRuleConfigurationMapper.selectList(lambdaQueryWrapper2);
            listMap.put(directoryConfiguration.getDirectoryCode() + directoryConfiguration.getDirectoryName()
                    + directoryConfiguration.getEmrRuleType(), documentRuleConfigurations);
        }

        int count = 0;
        for (DocumentDirectoryConfiguration directoryConfiguration : list) {
            documentRuleConfigurations = listMap.get(directoryConfiguration.getDirectoryCode()
                    + directoryConfiguration.getDirectoryName() + directoryConfiguration.getEmrRuleType());
            // 让他继续执行，好统计最后失败的数量
            // if (CollUtil.isEmpty(documentRuleConfigurations)) {
            // continue;
            // }
            count++;
            // 异步处理每个章节
            generateChapterWordFactory.generateChapterWord(documentRuleConfigurations, directoryConfiguration,
                    documentExport.getDataStartTime(), documentExport.getDataEndTime(), path, recordId,
                    secondMap.get(directoryConfiguration.getDirectoryCode().substring(0, 5)));
        }
        TransmittableThreadLocalManager.remove();
        return count;
    }

    @Override
    public R problemDataRemarks(DocumentExportRecordRuleDetail documentExportRecordRuleDetail) {
        documentExportRecordRuleDetailService.remove(Wrappers.<DocumentExportRecordRuleDetail>lambdaQuery()
                .eq(DocumentExportRecordRuleDetail::getExportRecordId,
                        documentExportRecordRuleDetail.getExportRecordId())
                .eq(DocumentExportRecordRuleDetail::getDirectoryCode, documentExportRecordRuleDetail.getDirectoryCode())
                .eq(DocumentExportRecordRuleDetail::getDirectoryName, documentExportRecordRuleDetail.getDirectoryName())
                .eq(DocumentExportRecordRuleDetail::getEmrRuleType, documentExportRecordRuleDetail.getEmrRuleType()));
        documentExportRecordRuleDetailService.save(documentExportRecordRuleDetail);
        return RUtil.success();
    }

    /**
     * 合并文档
     *
     * @param exportRecordId
     * @param exportRecord
     * @param levelCode
     * @param filePath
     * @param pathFileName
     */
    private void mergeDocuments(String exportRecordId, DocumentExportRecord exportRecord, String levelCode,
            String filePath, String pathFileName, String token) {
        WordUtils wordUtils = new WordUtils();
        ZipSecureFile.setMinInflateRatio(0);
        List<Map<String, String>> littleFiles = new ArrayList<>();
        LambdaQueryWrapper<DocumentDirectoryConfiguration> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.le(DocumentDirectoryConfiguration::getLevelCode, exportRecord.getExportDocumentLevel());
        lambdaQueryWrapper.isNotNull(DocumentDirectoryConfiguration::getLevelCode);
        lambdaQueryWrapper.apply("length(DIRECTORY_CODE) > {0}", "5");
        lambdaQueryWrapper.select(DocumentDirectoryConfiguration::getDirectoryCode,
                DocumentDirectoryConfiguration::getDirectoryName, DocumentDirectoryConfiguration::getLevelCode,
                DocumentDirectoryConfiguration::getEmrRuleType);
        List<DocumentDirectoryConfiguration> documentDirectoryConfigurations = documentDirectoryConfigurationService
                .list(lambdaQueryWrapper);

        // 所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = rulePermissionConfigurationService.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, "0")
                        .eq(StrUtil.isNotBlank(exportRecord.getProjectId()), RulePermissionConfiguration::getProjectId,
                                exportRecord.getProjectId())
                        .le(StrUtil.isNotBlank(levelCode), RulePermissionConfiguration::getLevelCode, levelCode));

        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream()
                .collect(Collectors.toMap(
                        item -> item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode(), item -> item,
                        (k1, k2) -> k1));
        List<DocumentDirectoryConfiguration> listFilter = documentDirectoryConfigurations;

        listFilter = listFilter.stream()
                .filter(item -> item.getDirectoryCode().length() < 6 || allAllocatedDirectoryMap
                        .containsKey(item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode()))
                .peek(item -> {
                    RulePermissionConfiguration rulePermissionConfiguration = allAllocatedDirectoryMap
                            .get(item.getDirectoryCode() + item.getDirectoryName() + item.getLevelCode());
                    if (rulePermissionConfiguration != null) {
                        item.setTaskStatus(rulePermissionConfiguration.getTaskStatus());
                        item.setPersonInCharge(rulePermissionConfiguration.getUserAccount());
                    }
                }).collect(Collectors.toList());

        Map<String, String> map;
        StringBuilder eachFileName;
        List<String> listFileNames = FileUtil.listFileNames(filePath);
        SysConfig sysConfigByName = RedisUtil.getSysConfigByName("hospital.code");
        String hospitalCode = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
        sysConfigByName = RedisUtil.getSysConfigByName("hospital.name");
        String hospitalName = sysConfigByName == null ? "" : sysConfigByName.getConfigValue();
        StringBuffer filePathAll;

        // 针对有说明的文档，更新！！！！！！！！！
        updateDoc(listFilter, filePath, exportRecordId);

        for (DocumentDirectoryConfiguration documentDirectoryConfiguration : listFilter) {
            filePathAll = new StringBuffer();
            map = new LinkedHashMap<>();
            eachFileName = new StringBuilder();
            eachFileName.append(documentDirectoryConfiguration.getDirectoryCode())
                    .append(HashCryptoFactory.encrypt(documentDirectoryConfiguration.getDirectoryName())).append("_")
                    .append(documentDirectoryConfiguration.getEmrRuleType());
            if (listFileNames.contains(eachFileName + ".docx") || listFileNames.contains(eachFileName + "_pre.docx")) {
                map.put("placeholder",
                        documentDirectoryConfiguration.getDirectoryCode()
                                + HashCryptoFactory.encrypt(documentDirectoryConfiguration.getDirectoryName()) + "_"
                                + documentDirectoryConfiguration.getEmrRuleType());
                filePathAll.append(filePath).append(eachFileName).append(".docx");
                // if (!listFileNames.contains(eachFileName + ".docx")) {
                // filePathAll.setLength(0);
                // filePathAll.append(filePath).append(eachFileName).append("_pre.docx");
                // }
                map.put("filePath", filePathAll.toString());
                littleFiles.add(map);
            }
        }
        // String templateFilePath = "word/电子病历评级" + levelCode + "级文档.docx";
        // String templateFilePath = filePath+"电子病历评级文档.docx";
        Map<String, String> params = new HashMap<>();
        params.put("year", DateUtil.date().year() + "");
        params.put("levelCode", LevelCodeEnum.fromCode(levelCode).getDescription());
        params.put("yearAndMouth", DateUtil.date().toString("yyyy年MM月"));
        params.put("hospitalCode", hospitalCode);
        params.put("hospitalName", hospitalName);
        // 为了进度
        params.put("exportRecordId", exportRecordId);
        params.put("token", token);
        // 嵌套文档
        wordUtils.mergeWordFiles(filePath, pathFileName, littleFiles, params);
    }

    @Override
    @Async("doExportExecutor")
    public void exportAsync(String exportRecordId, String username, String token) {
        // String token = TransmittableThreadLocalManager.get();
        // 替换固定目录下的文档

        String fileName;
        String pathFileName;
        FileInputStream fileInputStream = null;
        try {
            DocumentExportRecord exportRecord = documentExportRecordService.getById(exportRecordId);
            String levelCode = exportRecord.getExportDocumentLevel();
            String documentType = exportRecord.getExportDocumentType() == null ? ""
                    : exportRecord.getExportDocumentType();
            String filePath = SystemUtils.getFilePath() + "/word/" + exportRecordId + "/";

            fileName = "电子病历系统功能应用水平分级评价(" + exportRecord.getExportDocumentLevel() + "级).docx";
            pathFileName = filePath + fileName;

            // 创建四个CompletableFuture实例，分别对应你的四个方法
            CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
                // 合并文档
                mergeDocuments(exportRecordId, exportRecord, levelCode, filePath, pathFileName, token);
            }).exceptionally(ex -> {
                throw new BusinessException(ex.getMessage(), ex);
            });
            List<CompletableFuture> futureList = new ArrayList<>();
            futureList.add(future1);
            // 需要清除之前的那几个文档
            for (File file : FileUtil.loopFiles(filePath)) {
                if (file.getName().contains("电子病历系统功能应用水平分级评价")) {
                    file.delete();
                }
            }

            for (String type : documentType.split(",")) {
                if ("1".equals(type)) {
                    CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
                        // 导出excel文档
                        exportBaseExcel(exportRecord, filePath);
                    }).exceptionally(ex -> {
                        throw new BusinessException(ex.getMessage(), ex);
                    });
                    futureList.add(future2);
                }
                if ("2".equals(type)) {
                    CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
                        exportMedicaExcel(exportRecord, filePath);
                    }).exceptionally(ex -> {
                        throw new BusinessException(ex.getMessage(), ex);
                    });
                    futureList.add(future3);
                }
                if ("3".equals(type)) {
                    CompletableFuture<Void> future4 = CompletableFuture.runAsync(() -> {
                        exportQualityExcel(exportRecord, filePath);
                    }).exceptionally(ex -> {
                        throw new BusinessException(ex.getMessage(), ex);
                    });
                    futureList.add(future4);
                }
            }
            // 使用allOf方法将这四个Future组合成一个
            CompletableFuture[] futures = futureList.toArray(new CompletableFuture[futureList.size()]);
            CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(futures);
            // 等待所有任务执行完成
            combinedFuture.get();
            Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.processMap;
            if (processMap != null) {
                // 记录进度
                processMap.put(token, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, 1.0).build());
            }
        } catch (Exception e) {
            Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.processMap;
            if (processMap != null) {
                processMap.put(token, MapUtil.builder(new HashMap<String, Object>()).put(exportRecordId, -1.0).build());
            }
            throw new BusinessException(e.getMessage(), e);
        } finally {
            IoUtil.close(fileInputStream);
            // TransmittableThreadLocalManager.remove();
            delayQueueManager.put(new DelayTask(new TaskBaseEntity(token), 1000 * 100));
        }
        updateExportRecord(exportRecordId, username);
    }

    private void exportQualityExcel(DocumentExportRecord exportRecord, String filePath) {
        String directoryType = "3";
        String fileName = "电子病历系统功能应用水平分级评价(" + exportRecord.getExportDocumentLevel() + "级)_质量.xlsx";
        ExcelWriter writer = ExcelUtil.getWriter(filePath + fileName);
        List<DataDictionaryDirectoryConfigurationExportVo> listExportVos = getConfigurationExportVos(exportRecord,
                directoryType);
        CommonUtils.sortByDirectoryCode(listExportVos);
        CellStyle cellStyle = writer.getCellStyle();
        Font font = writer.createFont();
        font.setColor(Font.COLOR_RED);
        List<DataDictionaryDirectoryConfigurationExportVo> first = listExportVos.stream()
                .filter(entity -> entity.getDirectoryCode().length() == 2).collect(Collectors.toList());
        LinkedHashMap<String, List<DataDictionaryDirectoryConfigurationExportVo>> listMap = listExportVos.stream()
                .collect(Collectors.groupingBy(item -> item.getDirectoryCode().split("\\.")[0], LinkedHashMap::new,
                        Collectors.toList()));
        List<Object> row = CollUtil.newArrayList("编号", "名称", "实际总记录数", "符合要求记录数", "数据质量指数", "单位");
        boolean flag = true;
        for (DataDictionaryDirectoryConfigurationExportVo firstDirectoryConfiguration : first) {
            // 切换sheet，此时从第0行开始写
            if (flag) {
                writer.renameSheet(firstDirectoryConfiguration.getDirectoryName());
                flag = false;
            } else {
                writer.setSheet(firstDirectoryConfiguration.getDirectoryName());
            }
            writer.getSheet().setColumnWidth(0, 3000);
            writer.getSheet().setColumnWidth(1, 20000);
            writer.getSheet().setColumnWidth(2, 4000);
            writer.getSheet().setColumnWidth(3, 4000);
            writer.getSheet().setColumnWidth(4, 4000);
            writer.getSheet().setColumnWidth(5, 1500);
            writer.getCellStyle().setAlignment(HorizontalAlignment.LEFT);
            List<DataDictionaryDirectoryConfigurationExportVo> value = listMap
                    .get(firstDirectoryConfiguration.getDirectoryCode());
            // 每个sheet里面的内容
            int count = 0;
            // 每个标题的内容 一、病人管理与评估部分
            boolean flag1 = true;
            String directoryCode;
            String directoryName;
            for (DataDictionaryDirectoryConfigurationExportVo configuration : value) {
                directoryCode = configuration.getDirectoryCode();
                if (directoryCode.length() == 2) {
                    continue;
                }
                directoryName = configuration.getDirectoryName();
                if (directoryCode.length() == 5) {
                    // 标题 一、病人管理与评估部分
                    writer.merge(count, count, 0, 5, directoryName, true);
                    flag1 = true;
                } else if (directoryCode.length() >= 7) {
                    if (flag1) {
                        // 表头
                        writer.writeCellValue(0, count, row.get(0));
                        writer.writeCellValue(1, count, row.get(1));
                        writer.writeCellValue(2, count, row.get(2));
                        writer.writeCellValue(3, count, row.get(3));
                        writer.writeCellValue(4, count, row.get(4));
                        writer.writeCellValue(5, count, row.get(5));
                        count++;
                        flag1 = false;
                    }
                    CellStyle cellStyle1 = getMyCellStyle(writer, 4, count, cellStyle, font);
                    writer.writeCellValue(0, count, StringUtil.getValue(configuration.getDirectoryCode()));
                    writer.writeCellValue(1, count, StringUtil.getValue(directoryName));
                    writer.writeCellValue(2, count, StringUtil.getValue(configuration.getAllNum()));
                    if (!configuration.getDirectoryCode().endsWith(".")) {
                        writer.writeCellValue(3, count, "");
                        writer.writeCellValue(4, count, "");
                    } else {
                        writer.writeCellValue(3, count, StringUtil.getValue(configuration.getConditionalNum()));
                        BigDecimal ratio = StringUtil.getRatio(
                                StringUtil.getLongValue(configuration.getConditionalNum()),
                                StringUtil.getLongValue(configuration.getAllNum()));
                        writer.writeCellValue(4, count, ratio);
                        // 根据结果设置颜色
                        String coefficient = "0.5";
                        if (ratio.compareTo(new BigDecimal(coefficient)) < 0) {
                            writer.setStyle(cellStyle1, 4, count);
                        }
                    }
                    writer.writeCellValue(5, count, StringUtil.getValue(configuration.getDataUnit()));
                }
                count++;
            }
        }
        writer.close();
    }

    private static CellStyle getMyCellStyle(ExcelWriter writer, int x, int y, CellStyle cellStyle, Font font) {
        CellStyle cellStyle1 = writer.createCellStyle(x, y);
        cellStyle1.cloneStyleFrom(cellStyle);
        cellStyle1.setFont(font);
        return cellStyle1;
    }

    private void exportMedicaExcel(DocumentExportRecord exportRecord, String filePath) {
        String directoryType = "2";
        String fileName = "电子病历系统功能应用水平分级评价(" + exportRecord.getExportDocumentLevel() + "级)_病历.xlsx";
        ExcelWriter writer = ExcelUtil.getWriter(filePath + fileName);
        List<DataDictionaryDirectoryConfigurationExportVo> listExportVos = getConfigurationExportVos(exportRecord,
                directoryType);
        CommonUtils.sortByDirectoryCode(listExportVos);
        CellStyle cellStyle = writer.getCellStyle();

        Font font = writer.createFont();
        font.setColor(Font.COLOR_RED);

        List<DataDictionaryDirectoryConfigurationExportVo> first = listExportVos.stream()
                .filter(entity -> entity.getDirectoryCode().length() == 2).collect(Collectors.toList());
        LinkedHashMap<String, List<DataDictionaryDirectoryConfigurationExportVo>> listMap = listExportVos.stream()
                .collect(Collectors.groupingBy(item -> item.getDirectoryCode().split("\\.")[0], LinkedHashMap::new,
                        Collectors.toList()));
        List<Object> row = CollUtil.newArrayList("编号", "名称", "数量", "单位");
        Map<String, DataDictionaryDirectoryConfigurationExportVo> exportVoMap = listExportVos.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode(), item -> item,
                        (existing, replacement) -> replacement));
        boolean flag = true;
        for (DataDictionaryDirectoryConfigurationExportVo firstDirectoryConfiguration : first) {
            // 切换sheet，此时从第0行开始写
            if (flag) {
                writer.renameSheet(firstDirectoryConfiguration.getDirectoryName());
                flag = false;
            } else {
                writer.setSheet(firstDirectoryConfiguration.getDirectoryName());
            }
            writer.getSheet().setColumnWidth(0, 3000);
            writer.getSheet().setColumnWidth(1, 20000);
            writer.getSheet().setColumnWidth(2, 4000);
            writer.getSheet().setColumnWidth(3, 1500);
            writer.getCellStyle().setAlignment(HorizontalAlignment.LEFT);
            // 每个sheet里面的内容
            int count = 0;
            // 每个标题的内容 一、病人管理与评估部分
            String directoryCode;
            boolean flag1 = true;
            List<DataDictionaryDirectoryConfigurationExportVo> dataDictionaryDirectoryConfigurationExportVos = listMap
                    .get(firstDirectoryConfiguration.getDirectoryCode());
            for (DataDictionaryDirectoryConfigurationExportVo configuration : dataDictionaryDirectoryConfigurationExportVos) {
                directoryCode = configuration.getDirectoryCode();
                if (directoryCode.length() == 2) {
                    continue;
                }
                if (directoryCode.length() == 5) {
                    // 标题 一、病人管理与评估部分
                    writer.merge(count, count, 0, 3, configuration.getDirectoryName(), true);
                    flag1 = true;
                } else if (directoryCode.length() >= 6) {
                    if (flag1) {
                        writer.writeCellValue(0, count, row.get(0));
                        writer.writeCellValue(1, count, row.get(1));
                        writer.writeCellValue(2, count, row.get(2));
                        writer.writeCellValue(3, count, row.get(3));
                        count++;
                        flag1 = false;
                    }
                    CellStyle cellStyle1 = getMyCellStyle(writer, 3, count, cellStyle, font);
                    writer.writeCellValue(0, count, StringUtil.getValue(configuration.getDirectoryCode()));
                    writer.writeCellValue(1, count, StringUtil.getValue(configuration.getDirectoryName()));
                    writer.writeCellValue(2, count, StringUtil.getValue(configuration.getAllNum()));
                    // 防止最后一位被误填0
                    if (directoryCode.length() > 8) {
                        // 根据结果设置颜色
                        // 获取父级
                        // 02.01.3 （3级基础项）汇总
                        // 02.01.3.1 （1）从住院登记处接收病人基本信息，输入入院评估记录
                        String coefficient = "0.5";
                        DataDictionaryDirectoryConfigurationExportVo configurationExportVo = exportVoMap
                                .get(directoryCode.substring(0, directoryCode.lastIndexOf(".")));
                        if (configurationExportVo != null) {
                            BigDecimal ratio = StringUtil.getRatio(StringUtil.getLongValue(configuration.getAllNum()),
                                    StringUtil.getLongValue(configurationExportVo.getAllNum()));
                            // 根据结果设置颜色
                            if (ratio.compareTo(new BigDecimal(coefficient)) < 0) {
                                writer.setStyle(cellStyle1, 2, count);
                            }
                        }
                    }
                    writer.writeCellValue(3, count, StringUtil.getValue(configuration.getDataUnit()));
                }
                count++;
            }
        }
        writer.close();
    }

    private List<DataDictionaryDirectoryConfigurationExportVo> getConfigurationExportVos(
            DocumentExportRecord exportRecord, String directoryType) {
        List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService
                .list(new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                        .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, directoryType));
        // 过滤目录
        // 所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = rulePermissionConfigurationService.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, directoryType)
                        .eq(RulePermissionConfiguration::getProjectId, exportRecord.getProjectId()));
        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode() + item.getDirectoryName(), item -> item,
                        (k1, k2) -> k1));
        if ("2".equals(directoryType)) {
            list = list.stream().filter(
                    item -> item.getDirectoryCode().length() <= 6 || (StrUtil.isNotBlank(item.getAssociationLevel())
                            && item.getAssociationLevel().equals(exportRecord.getExportDocumentLevel())))
                    .collect(Collectors.toList());
        }
        List<DataDictionaryDirectoryConfiguration> listFilter = list;
        // if (StrUtil.isNotBlank(userAccount)) {
        // Set<String> userAllocatedDirectoryCodeSet =
        // rulePermissionConfigurations.stream().filter(item ->
        // item.getUserAccount().equals(userAccount)).map(item ->
        // item.getDirectoryCode() +
        // item.getDirectoryName()).collect(Collectors.toSet());
        // listFilter = list.stream().filter(item ->
        // userAllocatedDirectoryCodeSet.contains(item.getDirectoryCode() +
        // item.getDirectoryName())).collect(Collectors.toList());
        // }
        // 只要已分配的目录
        listFilter = listFilter.stream()
                .filter(item -> allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName()))
                .collect(Collectors.toList());
        Set<String> allowDirectoryCodes = new HashSet<>();
        String directoryCodeStr;
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : listFilter) {
            directoryCodeStr = directoryConfiguration.getDirectoryCode();
            if (directoryCodeStr.length() > 5) {
                allowDirectoryCodes.add(directoryCodeStr.substring(0, 2));
                allowDirectoryCodes.add(directoryCodeStr.substring(0, 5));
            }
            allowDirectoryCodes.add(directoryCodeStr);
        }
        list = list.stream().filter(item -> allowDirectoryCodes.contains(item.getDirectoryCode()))
                .collect(Collectors.toList());
        List<DataDictionaryDirectoryConfigurationExportVo> listExportVos = MapperUtils.INSTANCE
                .mapAsList(DataDictionaryDirectoryConfigurationExportVo.class, list);

        List<DataDictionaryDirectoryRuleConfiguration> configurations = dataDictionaryDirectoryRuleConfigurationService
                .list(new LambdaQueryWrapper<DataDictionaryDirectoryRuleConfiguration>()
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, directoryType)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, exportRecord.getProjectId()));
        Map<String, DataDictionaryDirectoryRuleConfiguration> directoryRuleConfigurationMap = configurations.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode() + "_" + item.getDirectoryName(), item -> item,
                        (k1, k2) -> k1));

        // 填充值
        List<DataScoreNumRecord> numRecords = dataScoreNumRecordService.list(Wrappers.<DataScoreNumRecord>lambdaQuery()
                .eq(DataScoreNumRecord::getDirectoryType, directoryType).eq(DataScoreNumRecord::getExecStatus, "0")
                .eq(DataScoreNumRecord::getExportRecordId, exportRecord.getId()));
        Map<String, DataScoreNumRecord> map = numRecords.stream().collect(Collectors
                .toMap(DataScoreNumRecord::getConfigurationId, record -> record, (existing, replacement) -> existing));
        listExportVos.forEach(item -> {
            // DataScoreNumRecord SQL执行结果是用的配置目录ID，关联用的配置ID（统一为配置ID）
            DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryRuleConfiguration = directoryRuleConfigurationMap
                    .get(item.getDirectoryCode() + "_" + item.getDirectoryName());
            if (dataDictionaryDirectoryRuleConfiguration == null) {
                return;
            }
            DataScoreNumRecord numRecord = map.get(dataDictionaryDirectoryRuleConfiguration.getId());
            if (numRecord != null) {
                item.setAllNum(numRecord.getAllNum());
                item.setConditionalNum(numRecord.getConditionalNum());
                item.setDataType(numRecord.getDataType());
                item.setExecStatus(numRecord.getExecStatus());
            }
        });
        return listExportVos;
    }

    private void exportBaseExcel(DocumentExportRecord exportRecord, String filePath) {
        String fileName = "电子病历系统功能应用水平分级评价(" + exportRecord.getExportDocumentLevel() + "级)_基本.xlsx";
        ExcelWriter writer = ExcelUtil.getWriter(filePath + fileName);
        writer.getSheet().setColumnWidth(1, 8000);
        writer.getCellStyle().setAlignment(HorizontalAlignment.LEFT);
        List<DataDictionaryDirectoryConfigurationExportVo> data = (List<DataDictionaryDirectoryConfigurationExportVo>) getBase(
                exportRecord.getId(), null).getData();
        Map<String, Object> row;
        List<Map<String, Object>> datas = new ArrayList<>();
        String directoryCode;
        for (DataDictionaryDirectoryConfigurationExportVo configurationExportVo : data) {
            row = new LinkedHashMap<>();
            directoryCode = configurationExportVo.getDirectoryCode();
            if (directoryCode.contains(".")) {
                directoryCode = "  " + directoryCode;
            }
            row.put("编号", directoryCode);
            row.put("名称", configurationExportVo.getDirectoryName());
            row.put("数量", configurationExportVo.getAllNum());
            row.put("单位", configurationExportVo.getDataUnit());
            datas.add(row);
        }
        writer.write(datas, true);
        writer.close();
    }

    @Override
    public R getCurrentProgress(String exportRecordId) {
        // 查询Redis中已经完成的数量
        Object finishNumObj = redisTemplate.opsForValue().get(exportRecordId);
        Integer finishNum = finishNumObj == null ? 0 : (Integer) finishNumObj;
        Object allDocxNumStr = redisTemplate.opsForValue().get(exportRecordId + "_ALLNUM");
        Integer allDocxNum = allDocxNumStr == null ? 0 : (Integer) allDocxNumStr;
        Map<String, Object> result = new HashMap<>();
        result.put("allDocxNum", allDocxNum);
        result.put("finishDocxNum", finishNum);
        Map<String, List<Map<String, Object>>> failureAndAlarmResult = new HashMap<>();
        // 最后一次才返回报错信息
        if (finishNum.equals(allDocxNum)) {
            List<Map<String, Object>> mapList = new ArrayList<>();
            List list = redisTemplate.opsForList().range(exportRecordId + "_RESULT", 0, -1);
            for (Object str : list) {
                mapList.add(JSONObject.parseObject((String) str));
            }
            failureAndAlarmResult.put("失败", mapList);

            List<Map<String, Object>> mapList2 = new ArrayList<>();
            list = redisTemplate.opsForList().range(exportRecordId + "_RESULT2", 0, -1);
            for (Object str : list) {
                mapList2.add(JSONObject.parseObject((String) str));
            }
            failureAndAlarmResult.put("告警", mapList2);
            // redisTemplate.delete(exportRecordId + "_RESULT2");
            // redisTemplate.delete(exportRecordId + "_RESULT");
            DocumentExportRecord documentExportRecord = documentExportRecordService.getById(exportRecordId);
            if (documentExportRecord != null) {
                documentExportRecord.setExportStatus("3");
                documentExportRecordService.updateById(documentExportRecord);
            }
            if (!redisTemplate.hasKey(exportRecordId + "_FINISH") && documentExportRecord != null) {
                dealConditional(documentExportRecord);
                redisTemplate.opsForValue().set(exportRecordId + "_FINISH", "1");
            }
        }
        result.put("failureAndAlarmResult", failureAndAlarmResult);
        return RUtil.success(result);
    }

    @SneakyThrows
    private void updateDoc(List<DocumentDirectoryConfiguration> filterList, String filePath, String exportRecordId) {
        // 看先哪些备注了的，如果备注了，需要重新生成
        LambdaQueryWrapper<DocumentExportRecordRuleDetail> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DocumentExportRecordRuleDetail::getExportRecordId, exportRecordId);
        List<DocumentExportRecordRuleDetail> list = documentExportRecordRuleDetailService.list(queryWrapper);
        Map<String, DocumentExportRecordRuleDetail> documentExportRecordRuleDetailMap = list.stream()
                .collect(Collectors.toMap(
                        item -> item.getDirectoryCode() + "_" + item.getDirectoryName() + "_" + item.getEmrRuleType(),
                        item -> item, (k1, k2) -> k1));
        String filePathAndName;
        DocumentExportRecordRuleDetail exportRecordRuleDetail;
        for (DocumentDirectoryConfiguration documentExportRecordRuleDetail : filterList) {
            filePathAndName = PathNameUtils.getFullPathFileName(filePath,
                    documentExportRecordRuleDetail.getDirectoryCode(),
                    documentExportRecordRuleDetail.getDirectoryName(), documentExportRecordRuleDetail.getEmrRuleType());
            // 找到文档，替换里面的值 医嘱记录中有药疗医嘱 182524 条记录，{{problemDataRemarks1}}。实际各个项目情况如下表：
            exportRecordRuleDetail = documentExportRecordRuleDetailMap
                    .get(documentExportRecordRuleDetail.getDirectoryCode() + "_"
                            + documentExportRecordRuleDetail.getDirectoryName() + "_"
                            + documentExportRecordRuleDetail.getEmrRuleType());
            String problemDataRemarks1 = exportRecordRuleDetail != null
                    ? "," + exportRecordRuleDetail.getProblemDataRemarks1()
                    : "";
            String problemDataRemarks2 = exportRecordRuleDetail != null
                    ? exportRecordRuleDetail.getProblemDataRemarks2()
                    : "";
            String problemDataRemarks3 = exportRecordRuleDetail != null
                    ? exportRecordRuleDetail.getProblemDataRemarks3()
                    : "";
            String problemDataRemarks4 = exportRecordRuleDetail != null
                    ? exportRecordRuleDetail.getProblemDataRemarks4()
                    : "";
            List<Map<String, Object>> remarks1 = new ArrayList();
            remarks1.add(new HashMap<String, Object>() {
                {
                    put("problemDataRemarks2", problemDataRemarks2);
                }
            });
            List<Map<String, Object>> remarks2 = new ArrayList();
            remarks2.add(new HashMap<String, Object>() {
                {
                    put("problemDataRemarks3", problemDataRemarks3);
                }
            });
            List<Map<String, Object>> remarks3 = new ArrayList();
            remarks3.add(new HashMap<String, Object>() {
                {
                    put("problemDataRemarks4", problemDataRemarks4);
                }
            });
            // 替换数据
            XWPFTemplate template = XWPFTemplate.compile(filePathAndName + "_pre.docx")
                    .render(new HashMap<String, Object>() {
                        {
                            put("problemDataRemarks1", problemDataRemarks1);
                            if (StrUtil.isNotBlank(problemDataRemarks2)) {
                                put("remarks1", remarks1);
                            }
                            if (StrUtil.isNotBlank(problemDataRemarks3)) {
                                put("remarks2", remarks2);
                            }
                            if (StrUtil.isNotBlank(problemDataRemarks4)) {
                                put("remarks3", remarks3);
                            }
                        }
                    });
            // 正式文件+文件类型
            filePathAndName = filePathAndName + ".docx";
            FileUtil.del(filePathAndName);
            template.writeAndClose(new FileOutputStream(filePathAndName));
        }
    }

    private void updateExportRecord(String exportRecordId, String username) {
        DocumentExportRecord exportRecord = documentExportRecordService.getById(exportRecordId);
        if (StrUtil.isNotBlank(username)) {
            exportRecord.setUpdateBy(username);
        }
        // 导出状态：1、预览；2、导出
        exportRecord.setExportStatus("2");
        documentExportRecordService.updateById(exportRecord);
    }

    @Override
    public R getBase(String exportRecordId, String userAccount) {
        // 根据编号排序，查询所有
        List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService
                .list(new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                        .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, 1));
        DocumentExportRecord exportRecord = documentExportRecordService.getById(exportRecordId);

        // 过滤目录
        // 所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = rulePermissionConfigurationService.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, "1")
                        .eq(RulePermissionConfiguration::getProjectId, exportRecord.getProjectId()));
        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode() + item.getDirectoryName(), item -> item,
                        (k1, k2) -> k1));
        List<DataDictionaryDirectoryConfiguration> listFilter = list;
        if (StrUtil.isNotBlank(userAccount)) {
            Set<String> userAllocatedDirectoryCodeSet = rulePermissionConfigurations.stream()
                    .filter(item -> item.getUserAccount().equals(userAccount))
                    .map(item -> item.getDirectoryCode() + item.getDirectoryName()).collect(Collectors.toSet());
            listFilter = list.stream().filter(
                    item -> userAllocatedDirectoryCodeSet.contains(item.getDirectoryCode() + item.getDirectoryName()))
                    .collect(Collectors.toList());
        }
        // 只要已分配的目录
        listFilter = listFilter.stream()
                .filter(item -> allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName()))
                .collect(Collectors.toList());
        Set<String> allowDirectoryCodes = new HashSet<>();
        String directoryCodeStr;
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : listFilter) {
            directoryCodeStr = directoryConfiguration.getDirectoryCode();
            allowDirectoryCodes.add(directoryCodeStr);
        }
        list = list.stream().filter(item -> allowDirectoryCodes.contains(item.getDirectoryCode()))
                .collect(Collectors.toList());

        List<DataDictionaryDirectoryRuleConfiguration> configurations = dataDictionaryDirectoryRuleConfigurationService
                .list(new LambdaQueryWrapper<DataDictionaryDirectoryRuleConfiguration>()
                        .eq(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, 1)
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, exportRecord.getProjectId()));
        Map<String, DataDictionaryDirectoryRuleConfiguration> directoryRuleConfigurationMap = configurations.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode() + "_" + item.getDirectoryName(), item -> item,
                        (k1, k2) -> k1));
        List<DataDictionaryDirectoryConfigurationExportVo> listExportVos = MapperUtils.INSTANCE
                .mapAsList(DataDictionaryDirectoryConfigurationExportVo.class, list);
        // 填充值
        List<DataScoreNumRecord> numRecords = dataScoreNumRecordService.list(Wrappers.<DataScoreNumRecord>lambdaQuery()
                .eq(DataScoreNumRecord::getDirectoryType, 1).eq(DataScoreNumRecord::getExecStatus, "0")
                .eq(DataScoreNumRecord::getExportRecordId, exportRecordId));

        Map<String, DataScoreNumRecord> map = numRecords.stream().collect(Collectors
                .toMap(DataScoreNumRecord::getConfigurationId, record -> record, (existing, replacement) -> existing));
        listExportVos.forEach(item -> {
            // DataScoreNumRecord SQL执行结果是用的配置目录ID，关联用的配置ID（统一为配置ID）
            DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryRuleConfiguration = directoryRuleConfigurationMap
                    .get(item.getDirectoryCode() + "_" + item.getDirectoryName());
            if (dataDictionaryDirectoryRuleConfiguration == null) {
                return;
            }
            DataScoreNumRecord numRecord = map.get(dataDictionaryDirectoryRuleConfiguration.getId());
            if (numRecord != null) {
                item.setAllNum(numRecord.getAllNum());
                item.setConditionalNum(numRecord.getConditionalNum());
                item.setDataType(numRecord.getDataType());
                item.setExecStatus(numRecord.getExecStatus());
            }
        });

        CommonUtils.sortByDirectoryCode(listExportVos);
        return RUtil.success(listExportVos);
    }

    @Override
    public R getMedicaRecordsAndQuality(String exportRecordId, Integer directoryType, String directoryCode,
            String userAccount) {
        // 根据编号排序，查询所有 同时按照一二级目录分组（树形）
        List<DataDictionaryDirectoryConfiguration> list = dataDictionaryDirectoryConfigurationService
                .list(new LambdaQueryWrapper<DataDictionaryDirectoryConfiguration>()
                        .eq(DataDictionaryDirectoryConfiguration::getDirectoryType, directoryType)
                        .likeRight(StrUtil.isNotBlank(directoryCode),
                                DataDictionaryDirectoryConfiguration::getDirectoryCode, directoryCode)
                        .orderByAsc(DataDictionaryDirectoryConfiguration::getDirectoryCode));
        DocumentExportRecord exportRecord = documentExportRecordService.getById(exportRecordId);
        // 过滤目录
        // 所有人已分配的目录
        List<RulePermissionConfiguration> rulePermissionConfigurations = rulePermissionConfigurationService.list(
                new LambdaQueryWrapper<RulePermissionConfiguration>()
                        .eq(RulePermissionConfiguration::getConfigType, directoryType)
                        .likeRight(StrUtil.isNotBlank(directoryCode), RulePermissionConfiguration::getDirectoryCode,
                                directoryCode)
                        .eq(RulePermissionConfiguration::getProjectId, exportRecord.getProjectId()));

        Map<String, RulePermissionConfiguration> allAllocatedDirectoryMap = rulePermissionConfigurations.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode() + item.getDirectoryName(), item -> item,
                        (k1, k2) -> k1));

        if (2 == directoryType) {
            list = list.stream().filter(
                    item -> item.getDirectoryCode().length() <= 6 || (StrUtil.isNotBlank(item.getAssociationLevel())
                            && item.getAssociationLevel().equals(exportRecord.getExportDocumentLevel())))
                    .collect(Collectors.toList());
        }
        List<DataDictionaryDirectoryConfiguration> listFilter = list;
        if (StrUtil.isNotBlank(userAccount)) {
            Set<String> userAllocatedDirectoryCodeSet = rulePermissionConfigurations.stream()
                    .filter(item -> item.getUserAccount().equals(userAccount))
                    .map(item -> item.getDirectoryCode() + item.getDirectoryName()).collect(Collectors.toSet());
            listFilter = list.stream().filter(
                    item -> userAllocatedDirectoryCodeSet.contains(item.getDirectoryCode() + item.getDirectoryName()))
                    .collect(Collectors.toList());
        }
        // 只要已分配的目录
        listFilter = listFilter.stream()
                .filter(item -> allAllocatedDirectoryMap.containsKey(item.getDirectoryCode() + item.getDirectoryName()))
                .collect(Collectors.toList());
        Set<String> allowDirectoryCodes = new HashSet<>();
        String directoryCodeStr;
        for (DataDictionaryDirectoryConfiguration directoryConfiguration : listFilter) {
            directoryCodeStr = directoryConfiguration.getDirectoryCode();
            if (directoryCodeStr.length() > 5) {
                allowDirectoryCodes.add(directoryCodeStr.substring(0, 2));
                allowDirectoryCodes.add(directoryCodeStr.substring(0, 5));
            }
            allowDirectoryCodes.add(directoryCodeStr);
        }
        list = list.stream().filter(item -> allowDirectoryCodes.contains(item.getDirectoryCode()))
                .collect(Collectors.toList());
        List<DataDictionaryDirectoryConfigurationExportVo> listExportVos = MapperUtils.INSTANCE
                .mapAsList(DataDictionaryDirectoryConfigurationExportVo.class, list);

        List<DataDictionaryDirectoryRuleConfiguration> configurations = dataDictionaryDirectoryRuleConfigurationService
                .list(new LambdaQueryWrapper<DataDictionaryDirectoryRuleConfiguration>()
                        .in(DataDictionaryDirectoryRuleConfiguration::getDirectoryType, new String[] { "2", "3" })
                        .eq(DataDictionaryDirectoryRuleConfiguration::getProjectId, exportRecord.getProjectId()));
        Map<String, DataDictionaryDirectoryRuleConfiguration> directoryRuleConfigurationMap = configurations.stream()
                .collect(Collectors.toMap(item -> item.getDirectoryCode() + "_" + item.getDirectoryName(), item -> item,
                        (k1, k2) -> k1));
        // 树形
        List<DataDictionaryDirectoryConfigurationExportVo> directorys;
        if (StrUtil.isBlank(directoryCode)) {
            directorys = listExportVos.stream().filter(entity -> entity.getDirectoryCode().length() == 2)
                    .collect(Collectors.toList());
        } else if (directoryCode.length() == 2) {
            directorys = listExportVos.stream()
                    .filter(entity -> entity.getDirectoryCode().length() <= 5 && entity.getDirectoryCode().length() > 2)
                    .collect(Collectors.toList());
        } else {
            directorys = listExportVos.stream().filter(entity -> entity.getDirectoryCode().length() > 5)
                    .collect(Collectors.toList());
            // 填充值
            List<DataScoreNumRecord> numRecords = dataScoreNumRecordService.list(
                    Wrappers.<DataScoreNumRecord>lambdaQuery().eq(DataScoreNumRecord::getDirectoryType, directoryType)
                            .eq(DataScoreNumRecord::getExecStatus, "0")
                            .eq(DataScoreNumRecord::getExportRecordId, exportRecordId));
            Map<String, DataScoreNumRecord> map = numRecords.stream().collect(Collectors.toMap(
                    DataScoreNumRecord::getConfigurationId, record -> record, (existing, replacement) -> existing));
            Map<String, DataDictionaryDirectoryConfigurationExportVo> exportVoMap = listExportVos.stream()
                    .collect(Collectors.toMap(item -> item.getDirectoryCode(), item -> item,
                            (existing, replacement) -> replacement));
            listExportVos.forEach(item -> {
                DataDictionaryDirectoryRuleConfiguration dataDictionaryDirectoryRuleConfiguration = directoryRuleConfigurationMap
                        .get(item.getDirectoryCode() + "_" + item.getDirectoryName());
                if (dataDictionaryDirectoryRuleConfiguration == null) {
                    return;
                }
                DataScoreNumRecord numRecord = map.get(dataDictionaryDirectoryRuleConfiguration.getId());
                String directoryCodeTemp = item.getDirectoryCode();
                if (numRecord == null) {
                    return;
                }
                item.setAllNum(numRecord.getAllNum());
                item.setDataType(numRecord.getDataType());
                item.setExecStatus(numRecord.getExecStatus());
                String coefficient = "0.5";
                // 01.01.3.
                // 目录类型（1、基础数据，2、病历数据，3、质量数据）
                if ("3".equals(item.getDirectoryType()) && item.getDirectoryCode().length() > 7
                        && item.getDirectoryCode().endsWith(".")) {
                    item.setConditionalNum(numRecord.getConditionalNum());
                    BigDecimal ratio = StringUtil.getRatio(StringUtil.getLongValue(numRecord.getConditionalNum()),
                            StringUtil.getLongValue(numRecord.getAllNum()));
                    // 根据结果设置颜色
                    if (ratio.compareTo(new BigDecimal(coefficient)) < 0) {
                        item.setDataQualityIndex(ratio + "(不足标准" + coefficient + ")");
                    } else {
                        item.setDataQualityIndex(ratio.toString());
                    }
                }
                if ("2".equals(item.getDirectoryType()) && directoryCodeTemp.length() > 8) {
                    // 获取父级
                    // 02.01.3 （3级基础项）汇总
                    // 02.01.3.1 （1）从住院登记处接收病人基本信息，输入入院评估记录
                    DataDictionaryDirectoryConfigurationExportVo configurationExportVo = exportVoMap
                            .get(directoryCodeTemp.substring(0, directoryCodeTemp.lastIndexOf(".")));
                    if (configurationExportVo != null) {
                        BigDecimal ratio = StringUtil.getRatio(StringUtil.getLongValue(numRecord.getAllNum()),
                                StringUtil.getLongValue(configurationExportVo.getAllNum()));
                        // 根据结果设置颜色
                        if (ratio.compareTo(new BigDecimal(coefficient)) < 0) {
                            item.setDataQualityIndex(ratio + "(不足标准" + coefficient + ")");
                        } else {
                            item.setDataQualityIndex(ratio.toString());
                        }
                    }
                }
            });
        }
        CommonUtils.sortByDirectoryCode(directorys);
        return RUtil.success(directorys);
    }

    @Override
    public R getMedicaRecordsOrQualityStatus(String exportRecordId, String directoryType) {
        DocumentExportRecord exportRecord = documentExportRecordService.getById(exportRecordId);
        if (exportRecord == null) {
            return RUtil.error("记录不存在");
        }
        List<DataDictionaryDirectoryConfigurationExportVo> listExportVos = getConfigurationExportVos(exportRecord,
                directoryType);
        CommonUtils.sortByDirectoryCode(listExportVos);
        /*
         * 计算是否通过：
         * 如果有一个不满足coefficient----0.5，直接不通过；
         * 如果都满足coefficient：
         * 基础---有基础项必须全部达标，每个基本项目的有效应用范围必须达到80%以上
         * 选择---考察选择项的目的是保证医疗机构中局部达标的项目数（基本项+选择项）整体上不低于全部项目的 2/3。选择项目的有效应用范围不应低于50%
         */
        // 目录类型（1、基础数据，2、病历数据，3、质量数据）
        R r;
        if ("3".equals(directoryType)) {
            r = dealQualityStatus(listExportVos);
        } else if ("2".equals(directoryType)) {
            r = dealMedicaRecordStatus(listExportVos);
        } else {
            throw new BusinessException("目录类型（2、病历数据，3、质量数据）不符合要求！");
        }
        return r;
    }

    private R dealMedicaRecordStatus(List<DataDictionaryDirectoryConfigurationExportVo> listExportVos) {
        Map<String, DataDictionaryDirectoryConfigurationExportVo> listMap2 = listExportVos.stream().collect(Collectors
                .toMap(item -> item.getDirectoryCode(), item -> item, (existing, replacement) -> replacement));

        String directoryCode;
        // 选择项未配置数量
        int notConfigNum1 = 0;
        // 选择项配置数量
        int configNum1 = 0;
        // 基础项未配置数量
        int notConfigNum2 = 0;
        // 基础项数量
        int configNum2 = 0;
        Map<String, Object> map = new HashMap<>();
        // 不满足质量系数
        List<DataDictionaryDirectoryConfigurationExportVo> notMatchDataQualityIndex = new ArrayList<>();
        // 不满足有效应用范围
        List<DataDictionaryDirectoryConfigurationExportVo> notMatchEffectiveApplicationScope = new ArrayList<>();
        /*
         * 病历编号规则：
         * 02 病房护士
         * 02.01 一、病人管理与评估
         * 02.01. 医院运行基础数据：医院当前展开的病区数（个）
         * 02.01.1 （1级选择项）输入的病人基本信息、住院记录作为护士本地工作记录
         * 02.01.2 （2级基础项）病人基本信息、住院记录等可提供本病房临床医师共享
         * 02.01.3 （3级基础项）汇总
         * 02.01.3.1 （1）从住院登记处接收病人基本信息，输入入院评估记录
         * 02.01.3.2 （2）床位、病情信息、病历资料供其他部门共享
         * 02.01.3.3 （3）转科或出院的出科信息在系统中处理
         * 质量编号规则：
         * 01 病房医师
         * 01.01 一、病房医嘱处理部分
         * 01.01.3 总记录数参考值：医院运行基础数据：医嘱记录数：
         * 01.01.3. （3级基础项）一致性：医嘱记录（医嘱项目编码，医嘱项目名称）
         * 01.01.4 总记录数参考值：医院运行基础数据：医嘱记录数：
         * 01.01.4. （4级基础项）完整性：医嘱记录（患者标识、医嘱号、医嘱分类、医嘱项目编码、医嘱项目名称、医嘱开始时间）
         * 01.01.5_1 总记录数参考值：医院运行基础数据：医嘱记录数：
         * 01.01.5_1. （5级基础项）完整性：医嘱记录（下达医嘱医师编码、下达医嘱医师姓名、医嘱状态）
         * 01.01.5_2 总记录数参考值：医院运行基础数据：药疗医嘱记录：
         * 01.01.5_2. （5级基础项）整合性：药疗医嘱记录与护理执行记录可对照（医嘱号、医嘱项目编码、药疗医嘱给药途径、药疗医嘱用法）
         */
        for (DataDictionaryDirectoryConfigurationExportVo configuration : listExportVos) {
            directoryCode = configuration.getDirectoryCode();
            if (directoryCode.length() == 2) {
                continue;
            }
            // 只取 02.01.3 （3级基础项）汇总
            // 不取他的下级 02.01.3.1 （1）从住院登记处接收病人基本信息，输入入院评估记录
            if (directoryCode.length() == 7) {
                // 选择项
                if ("1".equals(configuration.getDataType())) {
                    if (StrUtil.isBlank(configuration.getAssociatedType())) {
                        notConfigNum1++;
                        continue;
                    } else {
                        configNum1++;
                    }
                }
                if ("0".equals(configuration.getDataType())) {
                    if (StrUtil.isBlank(configuration.getAssociatedType())) {
                        notConfigNum2++;
                        continue;
                    } else {
                        configNum2++;
                    }
                }
                // 获取他的子级
                List<DataDictionaryDirectoryConfigurationExportVo> configurationExportVos = listExportVos.stream()
                        .filter(item -> !item.getDirectoryCode().equals(configuration.getDirectoryCode())
                                && item.getDirectoryCode().startsWith(configuration.getDirectoryCode()))
                        .collect(Collectors.toList());
                // 计算每一个的数据质量指数 能到只一步，都是默认配置了的
                for (DataDictionaryDirectoryConfigurationExportVo configurationExportVo : configurationExportVos) {
                    BigDecimal ratio = StringUtil.getRatio(StringUtil.getLongValue(configurationExportVo.getAllNum()),
                            StringUtil.getLongValue(configuration.getAllNum()));
                    // 根据结果设置颜色
                    String coefficient = "0.5";
                    if (ratio.compareTo(new BigDecimal(coefficient)) < 0) {
                        notMatchDataQualityIndex.add(configuration);
                        notMatchDataQualityIndex.add(configurationExportVo);
                    }
                }
                // 获取 02.01. 医院运行基础数据：医院当前展开的病区数（个），用于分母
                DataDictionaryDirectoryConfigurationExportVo configurationExportVo = listMap2
                        .get(directoryCode.substring(0, 6));
                if (configurationExportVo == null || StrUtil.isBlank(configurationExportVo.getAssociatedType())
                        || null == configurationExportVo.getAllNum() || 0 == configurationExportVo.getAllNum()) {
                    notMatchEffectiveApplicationScope.add(configuration);
                    continue;
                }

                if ("0".equals(configuration.getDataType())) {
                    // 每个基本项目的有效应用范围必须达到80%以上
                    if ((double) StringUtil.getLongValue(configuration.getConditionalNum())
                            / (double) configurationExportVo.getAllNum() > 0.8) {
                        notMatchEffectiveApplicationScope.add(configuration);
                    }
                } else {
                    // 每个选择项目的有效应用范围必须达到50%以上
                    if ((double) StringUtil.getLongValue(configuration.getConditionalNum())
                            / (double) configurationExportVo.getAllNum() >= 0.5) {
                        notMatchEffectiveApplicationScope.add(configuration);
                    }
                }
            }
        }
        notMatchDataQualityIndex.stream().filter(distinctByKey(p -> p.getDirectoryCode()));
        ;
        notMatchEffectiveApplicationScope.stream().filter(distinctByKey(p -> p.getDirectoryCode()));
        ;
        map.put("notMatchDataQualityIndex", notMatchDataQualityIndex);
        dealTitleRemark(notMatchDataQualityIndex, map, "notMatchDataQualityIndex");
        map.put("notMatchEffectiveApplicationScope", notMatchEffectiveApplicationScope);
        dealTitleRemark(notMatchEffectiveApplicationScope, map, "notMatchEffectiveApplicationScope");
        map.put("result", "0");
        if (notMatchDataQualityIndex.size() > 0) {
            map.put("result", "1");
            map.put("reason", "数据质量指数不满足标准");
            return RUtil.success(map);
        }
        if (notMatchEffectiveApplicationScope.size() > 0) {
            map.put("result", "1");
            map.put("reason", "有效应用范围不满足标准，基础项有效应用范围必须达到80%以上，选择项有效应用范围不应低于50%");
            return RUtil.success(map);
        }
        // 判断数量
        if (notConfigNum1 + notConfigNum2 + configNum1 + configNum2 == 0) {
            map.put("result", "1");
            map.put("reason", "未配置数据项");
            return RUtil.success(map);
        }
        if (notConfigNum2 > 0) {
            map.put("result", "1");
            map.put("reason", "配置基础项数量未达到要求");
            return RUtil.success(map);
        }
        double coefficient = (double) (configNum1 + configNum2)
                / (double) (notConfigNum1 + notConfigNum2 + configNum1 + configNum2);
        boolean isGreaterThanTwoThirds = coefficient >= 2.0 / 3.0;
        if (!isGreaterThanTwoThirds) {
            map.put("result", "1");
            map.put("reason", "配置选择项数量未达到要求");
            return RUtil.success(map);
        }
        return RUtil.success(map);
    }

    private static void dealTitleRemark(List<DataDictionaryDirectoryConfigurationExportVo> notMatchDataQualityIndex,
            Map<String, Object> map, String key) {
        if (CollUtil.isEmpty(notMatchDataQualityIndex)) {
            map.put(key + "First", new HashSet<>());
            map.put(key + "Second", new HashSet<>());
            map.put(key + "SecondDetail", new LinkedHashMap<>());
            return;
        }
        LinkedHashMap<String, List<DataDictionaryDirectoryConfigurationExportVo>> first = notMatchDataQualityIndex
                .stream().collect(Collectors.groupingBy(item -> item.getDirectoryCode().split("\\.")[0],
                        LinkedHashMap::new, Collectors.toList()));
        LinkedHashMap<String, List<DataDictionaryDirectoryConfigurationExportVo>> second = notMatchDataQualityIndex
                .stream()
                .collect(Collectors.groupingBy(
                        item -> item.getDirectoryCode().split("\\.")[0] + "." + item.getDirectoryCode().split("\\.")[1],
                        LinkedHashMap::new, Collectors.toList()));
        map.put(key + "First", first.keySet());
        map.put(key + "Second", second.keySet());
        map.put(key + "SecondDetail", second);
    }

    private R dealQualityStatus(List<DataDictionaryDirectoryConfigurationExportVo> listExportVos) {
        LinkedHashMap<String, List<DataDictionaryDirectoryConfigurationExportVo>> listMap = listExportVos.stream()
                .collect(Collectors.groupingBy(item -> item.getDirectoryCode().split("\\.")[0], LinkedHashMap::new,
                        Collectors.toList()));
        Map<String, DataDictionaryDirectoryConfigurationExportVo> listMap2 = listExportVos.stream().collect(Collectors
                .toMap(item -> item.getDirectoryCode(), item -> item, (existing, replacement) -> replacement));

        String directoryCode;
        // 选择项未配置数量
        int notConfigNum1 = 0;
        // 选择项配置数量
        int configNum1 = 0;
        // 基础项未配置数量
        int notConfigNum2 = 0;
        // 基础项数量
        int configNum2 = 0;
        List<DataDictionaryDirectoryConfigurationExportVo> value;
        Map<String, Object> map = new HashMap<>();
        // 不满足质量系数
        List<DataDictionaryDirectoryConfigurationExportVo> notMatchDataQualityIndex = new ArrayList<>();
        // 不满足有效应用范围
        List<DataDictionaryDirectoryConfigurationExportVo> notMatchEffectiveApplicationScope = new ArrayList<>();
        /*
         * 病历编号规则：
         * 02 病房护士
         * 02.01 一、病人管理与评估
         * 02.01. 医院运行基础数据：医院当前展开的病区数（个）
         * 02.01.1 （1级选择项）输入的病人基本信息、住院记录作为护士本地工作记录
         * 02.01.2 （2级基础项）病人基本信息、住院记录等可提供本病房临床医师共享
         * 02.01.3 （3级基础项）汇总
         * 02.01.3.1 （1）从住院登记处接收病人基本信息，输入入院评估记录
         * 02.01.3.2 （2）床位、病情信息、病历资料供其他部门共享
         * 02.01.3.3 （3）转科或出院的出科信息在系统中处理
         * 质量编号规则：
         * 01 病房医师
         * 01.01 一、病房医嘱处理部分
         * 01.01.3 总记录数参考值：医院运行基础数据：医嘱记录数：
         * 01.01.3. （3级基础项）一致性：医嘱记录（医嘱项目编码，医嘱项目名称）
         * 01.01.4 总记录数参考值：医院运行基础数据：医嘱记录数：
         * 01.01.4. （4级基础项）完整性：医嘱记录（患者标识、医嘱号、医嘱分类、医嘱项目编码、医嘱项目名称、医嘱开始时间）
         * 01.01.5_1 总记录数参考值：医院运行基础数据：医嘱记录数：
         * 01.01.5_1. （5级基础项）完整性：医嘱记录（下达医嘱医师编码、下达医嘱医师姓名、医嘱状态）
         * 01.01.5_2 总记录数参考值：医院运行基础数据：药疗医嘱记录：
         * 01.01.5_2. （5级基础项）整合性：药疗医嘱记录与护理执行记录可对照（医嘱号、医嘱项目编码、药疗医嘱给药途径、药疗医嘱用法）
         */
        for (Map.Entry<String, List<DataDictionaryDirectoryConfigurationExportVo>> entry : listMap.entrySet()) {
            value = entry.getValue();
            for (DataDictionaryDirectoryConfigurationExportVo configuration : value) {
                directoryCode = configuration.getDirectoryCode();
                if (directoryCode.length() == 2) {
                    continue;
                }
                if (directoryCode.length() > 7 && directoryCode.endsWith(".")) {
                    // 选择项
                    if ("1".equals(configuration.getDataType())) {
                        if (StrUtil.isBlank(configuration.getAssociatedType())
                                || StrUtil.isBlank(configuration.getConditionalAssociatedType())) {
                            notConfigNum1++;
                            continue;
                        } else {
                            configNum1++;
                        }
                    }
                    if ("0".equals(configuration.getDataType())) {
                        if (StrUtil.isBlank(configuration.getAssociatedType())
                                || StrUtil.isBlank(configuration.getConditionalAssociatedType())) {
                            notConfigNum2++;
                            continue;
                        } else {
                            configNum2++;
                        }
                    }
                    BigDecimal ratio = StringUtil.getRatio(StringUtil.getLongValue(configuration.getConditionalNum()),
                            StringUtil.getLongValue(configuration.getAllNum()));
                    // 根据结果设置颜色
                    String coefficient = "0.5";
                    if (ratio.compareTo(new BigDecimal(coefficient)) < 0) {
                        notMatchDataQualityIndex.add(configuration);
                    }

                    DataDictionaryDirectoryConfigurationExportVo configurationExportVo = listMap2
                            .get(directoryCode.substring(0, directoryCode.lastIndexOf(".")));
                    if (configurationExportVo == null || StrUtil.isBlank(configurationExportVo.getAssociatedType())
                            || null == configurationExportVo.getAllNum() || 0 == configurationExportVo.getAllNum()) {
                        notMatchEffectiveApplicationScope.add(configuration);
                        continue;
                    }
                    // 每个基本项目的有效应用范围必须达到80%以上
                    if ("0".equals(configuration.getDataType())) {
                        if ((double) StringUtil.getLongValue(configuration.getConditionalNum())
                                / (double) configurationExportVo.getAllNum() > 0.8) {
                            notMatchEffectiveApplicationScope.add(configuration);
                        }
                    } else {
                        if ((double) StringUtil.getLongValue(configuration.getConditionalNum())
                                / (double) configurationExportVo.getAllNum() >= 0.5) {
                            notMatchEffectiveApplicationScope.add(configuration);
                        }
                    }

                }
            }
        }
        notMatchDataQualityIndex.stream().filter(distinctByKey(p -> p.getDirectoryCode()));
        notMatchEffectiveApplicationScope.stream().filter(distinctByKey(p -> p.getDirectoryCode()));
        map.put("notMatchDataQualityIndex", notMatchDataQualityIndex);
        dealTitleRemark(notMatchDataQualityIndex, map, "notMatchDataQualityIndex");
        map.put("notMatchEffectiveApplicationScope", notMatchEffectiveApplicationScope);
        dealTitleRemark(notMatchEffectiveApplicationScope, map, "notMatchEffectiveApplicationScope");
        map.put("result", "0");
        if (notMatchDataQualityIndex.size() > 0) {
            map.put("result", "1");
            map.put("reason", "数据质量指数不满足标准");
            return RUtil.success(map);
        }
        if (notMatchEffectiveApplicationScope.size() > 0) {
            map.put("result", "1");
            map.put("reason", "有效应用范围不满足标准，基础项有效应用范围必须达到80%以上，选择项有效应用范围不应低于50%");
            return RUtil.success(map);
        }
        // 判断数量
        if (notConfigNum1 + configNum1 + configNum2 == 0) {
            map.put("result", "1");
            map.put("reason", "未配置数据项");
            return RUtil.success(map);
        }
        if (notConfigNum2 > 0) {
            map.put("result", "1");
            map.put("reason", "配置基础项数量未达到要求");
            return RUtil.success(map);
        }
        double coefficient = (double) (configNum1 + configNum2)
                / (double) (notConfigNum1 + notConfigNum2 + configNum1 + configNum2);
        boolean isGreaterThanTwoThirds = coefficient > 2.0 / 3.0;
        if (isGreaterThanTwoThirds) {
            map.put("result", "1");
            map.put("reason", "配置选择项数量未达到要求2/3");
            return RUtil.success(map);
        }
        return RUtil.success(map);
    }

    static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        // putIfAbsent方法添加键值对，如果map集合中没有该key对应的值，则直接添加，并返回null，如果已经存在对应的值，则依旧为原来的值。
        // 如果返回null表示添加数据成功(不重复)，不重复(null==null :TRUE)
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
